/**
 * Script para corrigir a URL do provider Goodwe no banco de dados
 * Remove o /api duplicado da base URL
 */

import {
  collection,
  doc,
  getDocs,
  query,
  updateDoc,
  where,
} from "firebase/firestore";
import { db } from "./lib/firebase";

async function fixGoodweUrl() {
  console.log("🔧 Iniciando correção da URL do provider Goodwe...");

  try {
    // Buscar o provider Goodwe
    const providersRef = collection(db, "providers");
    const goodweQuery = query(
      providersRef,
      where("provider_slug", "==", "goodwe")
    );
    const goodweSnapshot = await getDocs(goodweQuery);

    if (goodweSnapshot.empty) {
      console.log("❌ Provider Goodwe não encontrado");
      return;
    }

    const goodweDoc = goodweSnapshot.docs[0];
    const goodweData = goodweDoc.data();

    console.log(`📋 Provider encontrado: ${goodweData.name}`);
    console.log(`🌐 URL atual: ${goodweData.api_url}`);

    // Verificar se a URL precisa ser corrigida
    if (goodweData.api_url === "https://us.semsportal.com/api") {
      console.log("🔄 Corrigindo URL...");

      await updateDoc(doc(db, "providers", goodweDoc.id), {
        api_url: "https://us.semsportal.com",
        updatedAt: new Date().toISOString(),
      });

      console.log("✅ URL corrigida com sucesso!");
      console.log("🌐 Nova URL: https://us.semsportal.com");
    } else if (goodweData.api_url === "https://us.semsportal.com") {
      console.log("✅ URL já está correta!");
    } else {
      console.log(`⚠️ URL inesperada: ${goodweData.api_url}`);
    }
  } catch (error) {
    console.error("❌ Erro ao corrigir URL:", error);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  fixGoodweUrl()
    .then(() => {
      console.log("🎉 Script concluído!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Erro fatal:", error);
      process.exit(1);
    });
}

export { fixGoodweUrl };

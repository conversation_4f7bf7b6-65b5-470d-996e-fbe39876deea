import { cn } from "@/utils/merge-classes";

export default function CardStatus({
  status,
  inverter,
  img,
  color = "sky",
}: {
  status: number;
  inverter: { inverter_id: string; inverter_status: number }[];
  img?: string;
  color?: string;
}) {
  const classes = {
    card: `
    flex flex-row
    justify-between align-center
    rounded-xl shadow-lg
    bg-position-[110%] bg-no-repeat
    px-4 py-1 2xl:py-2
    `,
    container: `h-fit rounded-xl bg-white pt-2 -mt-3`,
  };

  const bgGradient = {
    sky: "border-sky-100 bg-gradient-to-l from-sky-50 to-sky-100",
    yellow: "border-yellow-100 bg-gradient-to-l from-yellow-50 to-yellow-100",
    orange: "border-orange-100 bg-gradient-to-l from-orange-50 to-orange-100",
    red: "border-red-100 bg-gradient-to-l from-red-50 to-red-100",
  };

  const statusText = [
    <span className="text-yellow-600">Alerta</span>,
    <span className="text-green-600">Ativo</span>,
    <span className="text-red-600">Inativo</span>,
  ];

  const inverterStatus = inverter.map((item) => {
    return (
      <div className="flex flex-col shrink-0">
        <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
          {item.inverter_id}
        </span>
        <span className="font-data font-extrabold italic text-slate-600 text-xl 2xl:text-2xl">
          {statusText[item.inverter_status]}
        </span>
      </div>
    );
  });

  return (
    <div
      className={cn(
        classes.container,
        bgGradient[color as keyof typeof bgGradient]
      )}
    >
      <div
        className={classes.card}
        style={img ? { backgroundImage: `url(/${img})` } : undefined}
      >
        <div className="flex flex-col shrink-0">
          <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
            Status Geral
          </span>
          <span className="font-data font-extrabold italic text-slate-600 text-2xl 2xl:text-3xl">
            {statusText[status]}
          </span>
        </div>
        {inverterStatus}
      </div>
    </div>
  );
}

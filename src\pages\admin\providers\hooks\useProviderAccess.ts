import { db } from "@/lib/firebase";
import type { CreateProviderDataAccess, ProviderDataAccess, UpdateProviderDataAccess } from "@/types";
import { addDoc, collection, deleteDoc, doc, getDocs, onSnapshot, orderBy, query, updateDoc, where } from "firebase/firestore";
import { useEffect, useState } from "react";

export function useProviderAccess(providerId?: string) {
  const [accessList, setAccessList] = useState<ProviderDataAccess[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAccess = async () => {
    try {
      setLoading(true);
      setError(null);

      const base = collection(db, "providersDataAccess");
      const q = providerId
        ? query(base, where("provider_id", "==", providerId), orderBy("provider_access"))
        : query(base, orderBy("provider_id"));
      const snap = await getDocs(q);
      const items = snap.docs.map(d => ({ id: d.id, ...d.data() })) as ProviderDataAccess[];
      setAccessList(items);
    } catch (err) {
      console.error("Erro ao carregar acessos de provider:", err);
      setError("Erro ao carregar acessos de provider");
    } finally {
      setLoading(false);
    }
  };

  const createAccess = async (data: CreateProviderDataAccess) => {
    const now = new Date().toISOString();
    const docRef = await addDoc(collection(db, "providersDataAccess"), {
      ...data,
      createdAt: now,
      updatedAt: now,
    });
    // Não atualizamos a lista local aqui pois o onSnapshot vai detectar a mudança
    return { id: docRef.id, ...data, createdAt: now, updatedAt: now } as ProviderDataAccess;
  };

  const updateAccess = async (id: string, data: UpdateProviderDataAccess) => {
    const now = new Date().toISOString();
    await updateDoc(doc(db, "providersDataAccess", id), { ...data, updatedAt: now });
    // Não atualizamos a lista local aqui pois o onSnapshot vai detectar a mudança
  };

  const deleteAccess = async (id: string) => {
    await deleteDoc(doc(db, "providersDataAccess", id));
    // Não atualizamos a lista local aqui pois o onSnapshot vai detectar a mudança
  };

  useEffect(() => {
    const base = collection(db, "providersDataAccess");
    const q = providerId
      ? query(base, where("provider_id", "==", providerId), orderBy("provider_access"))
      : query(base, orderBy("provider_id"));
    const unsub = onSnapshot(q, snap => {
      const items = snap.docs.map(d => ({ id: d.id, ...d.data() })) as ProviderDataAccess[];
      setAccessList(items);
      setLoading(false);
    }, err => {
      console.error("Erro no observador de acessos de provider:", err);
      setError("Erro ao sincronizar acessos");
      setLoading(false);
    });
    return () => unsub();
  }, [providerId]);

  return { accessList, loading, error, createAccess, updateAccess, deleteAccess, refetch: fetchAccess };
}



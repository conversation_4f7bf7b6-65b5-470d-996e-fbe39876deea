import { Button } from '@/components/ui/button';
import { checkDataExists, clearFirebase, seedFirebase } from '@/lib/firebase-seeds';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export function SeedButton() {
  const [isLoading, setIsLoading] = useState(false);
  const [dataStatus, setDataStatus] = useState<{
    providersExist: boolean;
    campusExist: boolean;
    usinasExist: boolean;
    hasData: boolean;
  } | null>(null);

  // Verificar status dos dados
  const checkStatus = async () => {
    try {
      const status = await checkDataExists();
      setDataStatus(status);
      return status;
    } catch (error) {
      console.error('Erro ao verificar status:', error);
      toast.error('Erro ao verificar status dos dados');
      return null;
    }
  };

  // Executar seed
  const handleSeed = async () => {
    setIsLoading(true);
    try {
      // Verificar se já existem dados
      const status = await checkStatus();

      if (status?.hasData) {
        toast.warning('Dados já existem no banco!');
        return;
      }

      // Executar seed
      const result = await seedFirebase();

      if (result.success) {
        toast.success(result.message);
        // Atualizar status
        await checkStatus();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Erro no seed:', error);
      toast.error('Erro ao executar seed do banco');
    } finally {
      setIsLoading(false);
    }
  };

  // Limpar banco (apenas desenvolvimento)
  const handleClear = async () => {
    if (!confirm('⚠️ ATENÇÃO: Esta operação remove TODOS os dados! Tem certeza?')) {
      return;
    }

    setIsLoading(true);
    try {
      const result = await clearFirebase();

      if (result.success) {
        toast.success(result.message);
        // Atualizar status
        await checkStatus();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Erro ao limpar:', error);
      toast.error('Erro ao limpar banco de dados');
    } finally {
      setIsLoading(false);
    }
  };

  // Verificar status ao montar componente
  useEffect(() => {
    checkStatus();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Button
          onClick={handleSeed}
          disabled={isLoading}
          className="bg-green-600 hover:bg-green-700"
        >
          {isLoading ? '🌱 Executando...' : '🌱 Executar Seed'}
        </Button>

        <Button
          onClick={handleClear}
          disabled={isLoading}
          variant="destructive"
          className="bg-red-600 hover:bg-red-700"
        >
          {isLoading ? '🧹 Limpando...' : '🧹 Limpar Banco'}
        </Button>

        <Button
          onClick={checkStatus}
          disabled={isLoading}
          variant="outline"
        >
          🔍 Verificar Status
        </Button>
      </div>

      {/* Status dos dados */}
      {dataStatus && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className={`p-3 rounded-lg border ${dataStatus.providersExist ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            }`}>
            <div className="text-sm font-medium">Providers</div>
            <div className={`text-lg font-bold ${dataStatus.providersExist ? 'text-green-600' : 'text-red-600'
              }`}>
              {dataStatus.providersExist ? '✅ Existem' : '❌ Não existem'}
            </div>
          </div>

          <div className={`p-3 rounded-lg border ${dataStatus.campusExist ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            }`}>
            <div className="text-sm font-medium">Campus</div>
            <div className={`text-lg font-bold ${dataStatus.campusExist ? 'text-green-600' : 'text-red-600'
              }`}>
              {dataStatus.campusExist ? '✅ Existem' : '❌ Não existem'}
            </div>
          </div>

          <div className={`p-3 rounded-lg border ${dataStatus.usinasExist ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            }`}>
            <div className="text-sm font-medium">Usinas</div>
            <div className={`text-lg font-bold ${dataStatus.usinasExist ? 'text-green-600' : 'text-red-600'
              }`}>
              {dataStatus.usinasExist ? '✅ Existem' : '❌ Não existem'}
            </div>
          </div>

          <div className={`p-3 rounded-lg border ${dataStatus.hasData ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            }`}>
            <div className="text-sm font-medium">Status Geral</div>
            <div className={`text-lg font-bold ${dataStatus.hasData ? 'text-green-600' : 'text-red-600'
              }`}>
              {dataStatus.hasData ? '✅ Completo' : '❌ Incompleto'}
            </div>
          </div>
        </div>
      )}

      {/* Informações */}
      <div className="text-sm text-gray-600 space-y-2">
        <p><strong>🌱 Seed:</strong> Cria todos os dados necessários para o funcionamento do sistema</p>
        <p><strong>🔍 Status:</strong> Verifica se os dados já existem no banco</p>
        <p><strong>🧹 Limpar:</strong> Remove todos os dados (apenas para desenvolvimento)</p>
      </div>
    </div>
  );
}

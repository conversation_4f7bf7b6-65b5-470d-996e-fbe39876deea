import { db } from "@/lib/firebase";
import type { CreateUsinaData, UpdateUsinaData, Usina } from "@/types";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  updateDoc,
  where
} from "firebase/firestore";
import { useEffect, useState } from "react";

export function useUsinas(campusId?: string) {
  const [usinas, setUsinas] = useState<Usina[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para carregar usinas (todas ou de um campus específico)
  const fetchUsinas = async () => {
    try {
      setLoading(true);
      setError(null);

      if (campusId) {
        // Buscar usinas de um campus específico
        const q = query(
          collection(db, "usinas"),
          where("campusId", "==", campusId),
          orderBy("name")
        );
        const querySnapshot = await getDocs(q);
        const usinasData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Usina[];
        setUsinas(usinasData);
      } else {
        // Buscar todas as usinas
        const q = query(collection(db, "usinas"), orderBy("name"));
        const querySnapshot = await getDocs(q);
        const usinasData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Usina[];
        setUsinas(usinasData);
      }
    } catch (err) {
      console.error("Erro ao carregar usinas:", err);
      setError("Erro ao carregar usinas");
    } finally {
      setLoading(false);
    }
  };

  // Função para criar uma nova usina
  const createUsina = async (usinaData: CreateUsinaData) => {
    try {
      const now = new Date().toISOString();
      const docRef = await addDoc(collection(db, "usinas"), {
        name: usinaData.name,
        capacity: usinaData.capacity,
        lat: usinaData.lat,
        long: usinaData.long,
        model: usinaData.model,
        module_qtd: usinaData.module_qtd,
        eficiency: usinaData.eficiency,
        area_m2: usinaData.area_m2,
        azimut: usinaData.azimut,
        provider_usina_id: usinaData.provider_usina_id,
        campusId: usinaData.campusId,
        providerId: usinaData.providerId,
        provider_access: usinaData.provider_access || "default",
        status: usinaData.status,
        createdAt: now,
        updatedAt: now,
      });

      const newUsina: Usina = {
        id: docRef.id,
        ...usinaData,
        createdAt: now,
        updatedAt: now,
      };

      setUsinas((prev) => [...prev, newUsina]);
      return newUsina;
    } catch (err) {
      console.error("Erro ao criar usina:", err);
      throw err;
    }
  };

  // Função para atualizar uma usina
  const updateUsina = async (id: string, usinaData: UpdateUsinaData) => {
    try {
      const usinaRef = doc(db, "usinas", id);
      const now = new Date().toISOString();
      await updateDoc(usinaRef, {
        name: usinaData.name,
        capacity: usinaData.capacity,
        lat: usinaData.lat,
        long: usinaData.long,
        model: usinaData.model,
        module_qtd: usinaData.module_qtd,
        eficiency: usinaData.eficiency,
        area_m2: usinaData.area_m2,
        azimut: usinaData.azimut,
        provider_usina_id: usinaData.provider_usina_id,
        campusId: usinaData.campusId,
        providerId: usinaData.providerId,
        provider_access: usinaData.provider_access || "default",
        updatedAt: now,
      });

      const updatedUsina: Usina = {
        id,
        ...usinaData,
        createdAt: usinas.find(u => u.id === id)?.createdAt || new Date().toISOString(),
        updatedAt: now,
      };

      setUsinas((prev) =>
        prev.map((usina) => (usina.id === id ? updatedUsina : usina))
      );

      return updatedUsina;
    } catch (err) {
      console.error("Erro ao atualizar usina:", err);
      throw err;
    }
  };

  // Função para deletar uma usina
  const deleteUsina = async (id: string) => {
    try {
      await deleteDoc(doc(db, "usinas", id));
      setUsinas((prev) => prev.filter((usina) => usina.id !== id));
      return { id };
    } catch (err) {
      console.error("Erro ao deletar usina:", err);
      throw err;
    }
  };

  // Função para buscar uma usina específica
  const getUsina = async (id: string) => {
    try {
      const usinaRef = doc(db, "usinas", id);
      const usinaDoc = await getDocs(query(collection(db, "usinas")));
      const usinaData = usinaDoc.docs.find(doc => doc.id === id);

      if (usinaData) {
        return { id: usinaData.id, ...usinaData.data() } as Usina;
      }
      return null;
    } catch (err) {
      console.error("Erro ao buscar usina:", err);
      throw err;
    }
  };

  // Carregar usinas na inicialização
  useEffect(() => {
    fetchUsinas();
  }, [campusId]);

  // Configurar observador para mudanças em tempo real
  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    if (campusId) {
      // Observar usinas de um campus específico
      const q = query(
        collection(db, "usinas"),
        where("campusId", "==", campusId),
        orderBy("name")
      );

      unsubscribe = onSnapshot(q, (querySnapshot) => {
        const usinasData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Usina[];

        setUsinas(usinasData);
        setLoading(false);
      }, (err) => {
        console.error("Erro no observador de usinas:", err);
        setError("Erro ao sincronizar usinas");
        setLoading(false);
      });
    } else {
      // Observar todas as usinas
      const q = query(collection(db, "usinas"), orderBy("name"));

      unsubscribe = onSnapshot(q, (querySnapshot) => {
        const usinasData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Usina[];

        setUsinas(usinasData);
        setLoading(false);
      }, (err) => {
        console.error("Erro no observador de usinas:", err);
        setError("Erro ao sincronizar usinas");
        setLoading(false);
      });
    }

    return () => unsubscribe?.();
  }, [campusId]);

  return {
    usinas,
    loading,
    error,
    createUsina,
    updateUsina,
    deleteUsina,
    getUsina,
    refetch: fetchUsinas,
  };
}

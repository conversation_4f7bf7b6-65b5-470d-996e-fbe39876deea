// Tipos para dados do campus e inversores

export interface InverterData {
  inverter_id: string;
  inverter_model: string;
  inverter_serial: string;
  inverter_capacity: number | null;
}

export interface CampusData {
  id: string;
  name: string;
  slug: string;
  coordinates: {
    lat: string;
    lng: string;
  };
  photo: string;
  capacity: number;
  station_id: string;
  energy_company: string;
  inverter: InverterData[];
}

import { Suspense, lazy } from "react";
import { Route, Routes } from "react-router-dom";

// Páginas com lazy loading
const ProvidersListPage = lazy(() => import("./pages/ProvidersListPage"));
const ProviderCreatePage = lazy(() => import("./pages/ProviderCreatePage"));
const ProviderEditPage = lazy(() => import("./pages/ProviderEditPage"));

export default function ProvidersRouter() {
  return (
    <Suspense fallback={<div className="p-4">Carregando...</div>}>
      <Routes>
        <Route path="/" element={<ProvidersListPage />} />
        <Route path="/create" element={<ProviderCreatePage />} />
        <Route path="/edit/:id" element={<ProviderEditPage />} />
      </Routes>
    </Suspense>
  );
}

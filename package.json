{"name": "sinergetico", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "firebase:emulators": "firebase emulators:start", "firebase:emulators:export": "firebase emulators:export ./firebase-data", "firebase:emulators:import": "firebase emulators:start --import=./firebase-data"}, "dependencies": {"@floating-ui/react": "^0.27.5", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/vite": "^4.1.1", "@tanstack/react-query": "^5.71.1", "@tanstack/react-query-devtools": "^5.71.2", "@tanstack/react-table": "^8.21.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "firebase": "^12.1.0", "framer-motion": "^12.6.3", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "prettier": "^3.5.3", "react": "^19.0.0", "react-datepicker": "^8.2.1", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-router-dom": "^7.4.1", "react-use": "^17.6.0", "recharts": "^2.15.1", "simplebar-react": "^3.3.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.1", "zod": "^4.0.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/node": "^22.13.17", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitejs/plugin-react": "^4.3.4", "esbuild": "^0.20.2", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}
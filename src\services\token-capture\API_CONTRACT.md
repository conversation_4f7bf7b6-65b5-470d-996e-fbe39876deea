# 📋 Contrato da API Externa - Sistema de Captura de Tokens

## 🔄 **Fluxo de Captura de Token**

### **1. Iniciar Job de Captura**
```http
POST /api/capture-token
Content-Type: application/json

{
  "providerSlug": "goodwe",
  "panelName": "default",
  "panelUrl": "https://www.semsportal.com/home/<USER>",
  "username": "<EMAIL>",
  "password": "Goodwe2018"
}
```

**Resposta Esperada:**
```json
{
  "success": true,
  "jobId": "job_12345_abcde",
  "message": "Captura de token iniciada",
  "status": "pending"
}
```

### **2. Verificar Status do Job**
```http
GET /api/capture-status/{jobId}
```

**Respostas Possíveis:**

#### **Job em Processamento:**
```json
{
  "success": true,
  "status": {
    "id": "job_12345_abcde",
    "status": "running",
    "progress": 50,
    "message": "Capturando token...",
    "startedAt": "2025-01-01T10:00:00.000Z"
  }
}
```

#### **Job Concluído com Sucesso:**
```json
{
  "success": true,
  "status": {
    "id": "job_12345_abcde",
    "status": "completed",
    "progress": 100,
    "message": "Token capturado com sucesso",
    "result": {
      "success": true,
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "cookies": ["sessionId=abc123", "authToken=xyz789"],
      "expiresAt": "2025-09-01T18:06:31.059Z"
    },
    "startedAt": "2025-01-01T10:00:00.000Z",
    "completedAt": "2025-01-01T10:02:30.000Z"
  }
}
```

#### **Job Falhou:**
```json
{
  "success": true,
  "status": {
    "id": "job_12345_abcde",
    "status": "failed",
    "progress": 100,
    "message": "Falha na captura: Credenciais inválidas",
    "error": "Credenciais inválidas",
    "startedAt": "2025-01-01T10:00:00.000Z",
    "completedAt": "2025-01-01T10:00:45.000Z"
  }
}
```

#### **Job Pendente:**
```json
{
  "success": true,
  "status": {
    "id": "job_12345_abcde",
    "status": "pending",
    "progress": 0,
    "message": "Aguardando execução",
    "startedAt": "2025-01-01T10:00:00.000Z"
  }
}
```

## ⏱️ **Configurações de Timeout**

- **Intervalo de Polling**: 5 segundos
- **Tentativas Máximas**: 60 (5 minutos total)
- **Timeout**: 5 minutos

## 📊 **Estados do Job**

| Status | Descrição | Ação do Sistema |
|--------|-----------|------------------|
| `pending` | Job criado, aguardando processamento | Aguarda próximo poll |
| `running` | Job em execução | Aguarda próximo poll |
| `completed` | Job concluído com sucesso | Retorna resultado |
| `failed` | Job falhou | Retorna erro |

## 🔧 **Implementação no Backend**

O backend implementa:

1. **Endpoint de Início**: `/api/capture-token`
2. **Endpoint de Status**: `/api/capture-status/{jobId}`
3. **Sistema de Jobs**: Processamento assíncrono
4. **Armazenamento**: Map em memória com limpeza automática
5. **Status**: pending → running → completed/failed

## 📝 **Exemplo de Uso**

```javascript
// Iniciar captura de token
const response = await fetch('/api/capture-token', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    providerSlug: 'goodwe',
    panelName: 'default',
    panelUrl: 'https://www.semsportal.com/home/<USER>',
    username: 'seu_usuario',
    password: 'sua_senha'
  })
});

const { jobId } = await response.json();

// Verificar status da captura
const statusResponse = await fetch(`/api/capture-status/${jobId}`);
const { status } = await statusResponse.json();

if (status.status === 'completed') {
  console.log('Token capturado:', status.result.token);
} else if (status.status === 'failed') {
  console.log('Erro:', status.error);
}
```

## 🚀 **Benefícios do Sistema**

1. **Não-bloqueante**: Frontend não trava aguardando
2. **Monitoramento**: Acompanha progresso em tempo real
3. **Timeout**: Evita espera infinita
4. **Retry**: Possibilidade de implementar retry automático
5. **Logs**: Rastreamento completo do processo

import React, { useState } from "react";

interface DateNavigatorProps {
  onDateChange: (date: number) => void;
  initialDate?: Date;
}

const weekDays = [
  "Domingo",
  "Segunda-feira",
  "Terça-feira",
  "Q<PERSON>rta-feira",
  "Q<PERSON><PERSON>-feira",
  "<PERSON><PERSON><PERSON>f<PERSON>",
  "Sábado",
];
const months = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

export const DateNavigator: React.FC<DateNavigatorProps> = ({
  onDateChange,
  initialDate,
}) => {
  const today = new Date(); // Data fixa conforme contexto
  const [currentDate, setCurrentDate] = useState<Date>(initialDate || today);

  const goToPreviousDay = () => {
    const prev = new Date(currentDate);
    prev.setDate(prev.getDate() - 1);
    setCurrentDate(prev);
    // onDateChange(prev);
    onDateChange(-1);
  };

  const goToNextDay = () => {
    if (!isToday(currentDate)) {
      const next = new Date(currentDate);
      next.setDate(next.getDate() + 1);
      setCurrentDate(next);
      onDateChange(1);
    }
  };

  const isToday = (date: Date) => {
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  return (
    <div className="flex items-center gap-2">
      {/* Botão voltar */}
      <button
        onClick={goToPreviousDay}
        className="rounded-full p-2 bg-slate-100 hover:bg-slate-200 transition hover:cursor-pointer"
        aria-label="Dia anterior"
      >
        <span className="text-xl font-bold text-slate-600">&lt;</span>
      </button>

      {/* Dia */}
      <div className="mx-2 flex flex-col items-center">
        <span className="text-7xl font-extrabold font-data text-slate-800 leading-none">
          {currentDate.getDate()}
        </span>
      </div>

      {/* Mês e dia da semana */}
      <div className="flex flex-col ml-2 min-w-[100px]">
        <span className="font-display text-lg text-slate-400 font-light uppercase tracking-wide">
          {months[currentDate.getMonth()]}
        </span>
        <span className="font-display text-slate-600">
          {weekDays[currentDate.getDay()]}
        </span>
      </div>

      {/* Botão avançar */}
      <button
        onClick={goToNextDay}
        disabled={isToday(currentDate)}
        className={`rounded-full p-2 ml-2 transition ${isToday(currentDate) ? "bg-slate-50 text-slate-300 cursor-not-allowed" : "bg-slate-100 hover:bg-slate-200 text-slate-600 hover:cursor-pointer"}`}
        aria-label="Próximo dia"
      >
        <span className="text-xl font-bold">&gt;</span>
      </button>
    </div>
  );
};

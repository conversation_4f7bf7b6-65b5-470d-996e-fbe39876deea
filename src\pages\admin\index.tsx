import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ArrowRight,
  BatteryCharging,
  Building2,
  Settings,
  Users,
  Zap
} from "lucide-react";
import { Link, Route, Routes } from "react-router-dom";
import CampusRouter from "./campus";
import { ModuleDataSummary, useDataDebugInfo } from "./components/DataAdmin";
import ProviderStatus from "./components/ProviderStatus";
import SeedManager from "./components/SeedManager";
import ProvidersRouter from "./providers";

export default function AdminRouter() {
  return (
    <div className="space-y-6">
      <AdminNav />
      <Routes>
        <Route path="/" element={<AdminDashboard />} />
        <Route path="/campus/*" element={<CampusRouter />} />
        <Route path="/providers/*" element={<ProvidersRouter />} />
      </Routes>
    </div>
  );
}

function AdminDashboard() {
  const { data, loading, errors } = useDataDebugInfo();

  const adminModules = [
    {
      title: "Campus",
      moduleType: "campus",
      description: "Gerencie os campus do IFRS",
      href: "/admin/campus",
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      disabled: false,
    },
    {
      title: "Fabricantes",
      moduleType: "providers",
      description: "Configure fabricantes dos equipamentos",
      href: "/admin/providers",
      icon: BatteryCharging,
      color: "text-green-600",
      bgColor: "bg-green-50",
      disabled: false,
    },
    {
      title: "Companhias de Energia",
      moduleType: "energy_companies",
      description: "Gerencie companhias de energia",
      href: "/admin/companies",
      icon: Zap,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      disabled: true,
    },
    {
      title: "Usuários",
      moduleType: "users",
      description: "Gerencie usuários do sistema",
      href: "/admin/users",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      disabled: true,
    },
    {
      title: "Configurações",
      moduleType: "settings",
      description: "Configurações gerais do sistema",
      href: "/admin/settings",
      icon: Settings,
      color: "text-gray-600",
      bgColor: "bg-gray-50",
      disabled: true,
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Painel Administrativo</h1>
        <p className="text-gray-600 mt-2">
          Gerencie campus, providers e configurações do sistema
        </p>
      </div>

      {/* Gerenciador de Dados (apenas para desenvolvimento) */}
      {import.meta.env.DEV && <SeedManager />}

      {/* Status dos Providers (apenas para desenvolvimento) */}
      {import.meta.env.DEV && <ProviderStatus />}

      {/* Módulos de Administração */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adminModules.map((module) => {
          const Icon = module.icon;

          return (
            <Card key={module.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3 flex flex-row gap-4 items-center">
                <div className={`w-12 h-12 rounded-lg ${module.bgColor} flex items-center justify-center mb-3`}>
                  <Icon className={`h-6 w-6 ${module.color}`} />
                </div>
                <div>
                  <CardTitle className="mb-1">{module.title}</CardTitle>
                  <CardDescription>{module.description}</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                {/* Botão de ação */}
                {module.disabled ? (
                  <Button disabled className="w-full my-3">
                    Em Breve
                  </Button>
                ) : (
                  <Link to={module.href}>
                    <Button className="w-full my-3 group">
                      Acessar
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                )}

                {/* Dados do módulo - apenas para módulos com dados */}
                {['campus', 'providers'].includes(module.moduleType) && (
                  loading ? (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                    </div>
                  ) : (
                    <ModuleDataSummary
                      moduleType={module.moduleType as 'campus' | 'providers'}
                      data={data}
                      errors={errors}
                    />
                  )
                )}

              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Informações do Sistema */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informações do Sistema</CardTitle>
          <CardDescription>
            Detalhes sobre a configuração e status atual
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-2">Ambiente</h4>
              <div className="flex items-center gap-2">
                <Badge variant={import.meta.env.DEV ? "default" : "secondary"}>
                  {import.meta.env.DEV ? "Desenvolvimento" : "Produção"}
                </Badge>
                {import.meta.env.DEV && (
                  <span className="text-xs text-gray-500">
                    (Emulador Firebase)
                  </span>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-2">Versão</h4>
              <p className="text-sm text-gray-600">1.0.0</p>
            </div>

            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-2">Última Atualização</h4>
              <p className="text-sm text-gray-600">
                {new Date().toLocaleDateString('pt-BR')}
              </p>
            </div>

            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-2">Provedor Principal</h4>
              <p className="text-sm text-gray-600">Goodwe (SEMS Portal)</p>
            </div>
          </div>

          {import.meta.env.DEV && (
            <div className="border-t pt-4">
              <h4 className="font-medium text-sm text-gray-700 mb-2">Modo Desenvolvimento</h4>
              <p className="text-xs text-gray-500">
                • Emulador Firebase ativo para testes locais<br />
                • Ferramentas de desenvolvimento habilitadas<br />
                • Sistema de scraping Goodwe configurado<br />
                • Dados de exemplo disponíveis
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function AdminNav() {
  return (
    <div className="bg-white border rounded-lg px-4 py-3 flex items-center gap-3 sticky top-0 z-10">
      <a href="/admin" className="text-sm text-gray-700 hover:text-gray-900">Início</a>
      <span className="text-gray-300">|</span>
      <a href="/admin/providers" className="text-sm text-gray-700 hover:text-gray-900">Providers</a>
      <span className="text-gray-300">|</span>
      <a href="/admin/campus" className="text-sm text-gray-700 hover:text-gray-900">Campus</a>
    </div>
  );
}

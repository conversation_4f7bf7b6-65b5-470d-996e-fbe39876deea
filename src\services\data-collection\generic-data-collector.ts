import { APICallResult } from "@/types/services";
import { BaseDataCollector } from "./base-data-collector";

export class GenericDataCollector extends BaseDataCollector {
  constructor(providerId: string, apiToken: string, baseUrl?: string) {
    super(providerId, apiToken, baseUrl || "");
  }

  async collectUsinaData(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult[]> {
    const results: APICallResult[] = [];

    try {
      // Endpoints padrão para coleta de dados
      const endpoints = [
        "/api/usina/status",
        "/api/usina/producao",
        "/api/usina/energia",
        "/api/usina/performance",
      ];

      for (const endpoint of endpoints) {
        try {
          const result = await this.callEndpoint(
            endpoint,
            usinaId,
            providerUsinaId
          );
          results.push(result);
        } catch (error) {
          console.error(`Erro ao chamar endpoint ${endpoint}:`, error);

          // Adiciona resultado de erro
          results.push({
            id: `${usinaId}_${endpoint}_${Date.now()}`,
            jobId: "",
            campusId: usinaId, // Usando usinaId como campusId temporariamente
            usinaId,
            providerId: this.providerId,
            endpoint,
            timestamp: new Date(),
            data: null,
            rawResponse: "",
            success: false,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }
    } catch (error) {
      console.error(
        `Erro geral na coleta de dados da usina ${usinaId}:`,
        error
      );
    }

    return results;
  }

  private async callEndpoint(
    endpoint: string,
    usinaId: string,
    campusId: string
  ): Promise<APICallResult> {
    const url = `${this.baseUrl}${endpoint}`;

    try {
      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.apiToken}`,
          "Content-Type": "application/json",
          "X-Usina-ID": usinaId,
          "X-Campus-ID": campusId,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const rawResponse = await response.text();
      const data = JSON.parse(rawResponse);

      return {
        id: `${usinaId}_${endpoint}_${Date.now()}`,
        jobId: "",
        campusId,
        usinaId,
        providerId: this.providerId,
        endpoint,
        timestamp: new Date(),
        data,
        rawResponse,
        success: true,
      };
    } catch (error) {
      throw new Error(`Falha na chamada para ${endpoint}: ${error}`);
    }
  }

  // Método para coletar dados específicos de um provider
  async collectProviderSpecificData(
    usinaId: string,
    campusId: string,
    customEndpoints?: string[]
  ): Promise<APICallResult[]> {
    if (customEndpoints && customEndpoints.length > 0) {
      // Usar endpoints customizados se fornecidos
      const results: APICallResult[] = [];

      for (const endpoint of customEndpoints) {
        try {
          const result = await this.callEndpoint(endpoint, usinaId, campusId);
          results.push(result);
        } catch (error) {
          console.error(
            `Erro ao chamar endpoint customizado ${endpoint}:`,
            error
          );
        }
      }

      return results;
    } else {
      // Usar endpoints padrão
      return this.collectUsinaData(usinaId, campusId);
    }
  }
}

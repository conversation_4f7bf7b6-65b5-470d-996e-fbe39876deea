import { db } from "@/lib/firebase";
import type { CreateProviderData, Provider, UpdateProviderData } from "@/types";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  updateDoc
} from "firebase/firestore";
import { useEffect, useState } from "react";

export function useProviders() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para carregar todos os providers
  const fetchProviders = async () => {
    try {
      setLoading(true);
      setError(null);

      const q = query(collection(db, "providers"), orderBy("name"));
      const querySnapshot = await getDocs(q);
      const providersData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Provider[];

      setProviders(providersData);
    } catch (err) {
      console.error("Erro ao carregar providers:", err);
      setError("Erro ao carregar providers");
    } finally {
      setLoading(false);
    }
  };

  // Função para criar um novo provider
  const createProvider = async (providerData: CreateProviderData) => {
    try {
      const now = new Date().toISOString();
      const docRef = await addDoc(collection(db, "providers"), {
        name: providerData.name,
        api_url: providerData.api_url,
        api_token: providerData.api_token,
        status: providerData.status,
        createdAt: now,
        updatedAt: now,
      });

      const newProvider: Provider = {
        id: docRef.id,
        ...providerData,
        createdAt: now,
        updatedAt: now,
      };

      setProviders((prev) => [...prev, newProvider]);
      return newProvider;
    } catch (err) {
      console.error("Erro ao criar provider:", err);
      throw err;
    }
  };

  // Função para atualizar um provider
  const updateProvider = async (
    id: string,
    providerData: UpdateProviderData
  ) => {
    try {
      const providerRef = doc(db, "providers", id);
      const now = new Date().toISOString();
      await updateDoc(providerRef, {
        ...providerData,
        updatedAt: now,
      });

      const updatedProvider: Provider = {
        id,
        ...providerData,
        createdAt: providers.find(p => p.id === id)?.createdAt || new Date().toISOString(),
        updatedAt: now,
      };

      setProviders((prev) =>
        prev.map((provider) => (provider.id === id ? updatedProvider : provider))
      );

      return updatedProvider;
    } catch (err) {
      console.error("Erro ao atualizar provider:", err);
      throw err;
    }
  };

  // Função para deletar um provider
  const deleteProvider = async (id: string) => {
    try {
      await deleteDoc(doc(db, "providers", id));
      setProviders((prev) => prev.filter((provider) => provider.id !== id));
      return { id };
    } catch (err) {
      console.error("Erro ao deletar provider:", err);
      throw err;
    }
  };

  // Função para buscar um provider específico
  const getProvider = async (id: string) => {
    try {
      const providerRef = doc(db, "providers", id);
      const providerDoc = await getDocs(query(collection(db, "providers")));
      const providerData = providerDoc.docs.find(doc => doc.id === id);

      if (providerData) {
        return { id: providerData.id, ...providerData.data() } as Provider;
      }
      return null;
    } catch (err) {
      console.error("Erro ao buscar provider:", err);
      throw err;
    }
  };

  // Carregar providers na inicialização
  useEffect(() => {
    fetchProviders();
  }, []);

  // Configurar observador para mudanças em tempo real
  useEffect(() => {
    const q = query(collection(db, "providers"), orderBy("name"));

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const providersData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Provider[];

      setProviders(providersData);
      setLoading(false);
    }, (err) => {
      console.error("Erro no observador de providers:", err);
      setError("Erro ao sincronizar providers");
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return {
    providers,
    loading,
    error,
    createProvider,
    updateProvider,
    deleteProvider,
    getProvider,
    refetch: fetchProviders,
  };
}

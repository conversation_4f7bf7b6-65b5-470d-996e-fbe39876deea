import { db } from "@/lib/firebase";
import type { Campus, CreateCampusData, UpdateCampusData } from "@/types";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  updateDoc
} from "firebase/firestore";
import { useEffect, useState } from "react";

export function useCampus() {
  const [campus, setCampus] = useState<Campus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para carregar todos os campus
  const fetchCampus = async () => {
    try {
      setLoading(true);
      setError(null);

      const q = query(collection(db, "campus"), orderBy("name"));
      const querySnapshot = await getDocs(q);
      const campusData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Campus[];

      setCampus(campusData);
    } catch (err) {
      console.error("Erro ao carregar campus:", err);
      setError("Erro ao carregar campus");
    } finally {
      setLoading(false);
    }
  };

  // Função para criar um novo campus
  const createCampus = async (campusData: CreateCampusData) => {
    try {
      console.log("🔍 Tentando criar campus:", campusData);

      const now = new Date().toISOString();
      const campusDoc = {
        name: campusData.name,
        city: campusData.city,
        lat: campusData.lat,
        long: campusData.long,
        status: campusData.status,
        maintenanceMode: campusData.maintenanceMode ?? false,
        createdAt: now,
        updatedAt: now,
      };

      console.log("📝 Dados do documento:", campusDoc);
      console.log("🔗 Conectando ao Firestore...");

      const docRef = await addDoc(collection(db, "campus"), campusDoc);

      console.log("✅ Documento criado com ID:", docRef.id);

      const newCampus: Campus = {
        id: docRef.id,
        ...campusData,
        createdAt: now,
        updatedAt: now,
      };

      console.log("🔄 Atualizando estado local...");
      setCampus((prev) => [...prev, newCampus]);

      console.log("🎉 Campus criado com sucesso:", newCampus);
      return newCampus;
    } catch (err) {
      console.error("❌ Erro ao criar campus:", err);
      console.error("🔍 Detalhes do erro:", {
        name: err instanceof Error ? err.name : 'Unknown',
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : 'No stack trace'
      });
      throw err;
    }
  };

  // Função para atualizar um campus
  const updateCampus = async (id: string, campusData: UpdateCampusData) => {
    try {
      const campusRef = doc(db, "campus", id);
      const now = new Date().toISOString();
      await updateDoc(campusRef, {
        name: campusData.name,
        city: campusData.city,
        lat: campusData.lat,
        long: campusData.long,
        status: campusData.status,
        maintenanceMode: campusData.maintenanceMode ?? false,
        updatedAt: now,
      });

      const updatedCampus: Campus = {
        id,
        ...campusData,
        createdAt: campus.find(c => c.id === id)?.createdAt || new Date().toISOString(),
        updatedAt: now,
      };

      setCampus((prev) => prev.map((c) => (c.id === id ? updatedCampus : c)));
      return updatedCampus;
    } catch (err) {
      console.error("Erro ao atualizar campus:", err);
      throw err;
    }
  };

  // Função para deletar um campus
  const deleteCampus = async (id: string) => {
    try {
      await deleteDoc(doc(db, "campus", id));
      setCampus((prev) => prev.filter((c) => c.id !== id));
      return { id };
    } catch (err) {
      console.error("Erro ao deletar campus:", err);
      throw err;
    }
  };

  // Função para buscar um campus específico
  const getCampus = async (id: string) => {
    try {
      const campusRef = doc(db, "campus", id);
      const campusDoc = await getDocs(query(collection(db, "campus")));
      const campusData = campusDoc.docs.find(doc => doc.id === id);

      if (campusData) {
        return { id: campusData.id, ...campusData.data() } as Campus;
      }
      return null;
    } catch (err) {
      console.error("Erro ao buscar campus:", err);
      throw err;
    }
  };

  // Função para buscar campus com suas usinas
  const getCampusWithUsinas = async (id: string) => {
    try {
      const campusRef = doc(db, "campus", id);
      const campusDoc = await getDocs(query(collection(db, "campus")));
      const campusData = campusDoc.docs.find(doc => doc.id === id);

      if (campusData) {
        return { id: campusData.id, ...campusData.data() } as Campus;
      }
      return null;
    } catch (err) {
      console.error("Erro ao buscar campus com usinas:", err);
      throw err;
    }
  };

  // Carregar campus na inicialização
  useEffect(() => {
    fetchCampus();
  }, []);

  // Configurar observador para mudanças em tempo real
  useEffect(() => {
    const q = query(collection(db, "campus"), orderBy("name"));

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const campusData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Campus[];

      setCampus(campusData);
      setLoading(false);
    }, (err) => {
      console.error("Erro no observador de campus:", err);
      setError("Erro ao sincronizar campus");
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return {
    campus,
    loading,
    error,
    createCampus,
    updateCampus,
    deleteCampus,
    getCampus,
    getCampusWithUsinas,
    refetch: fetchCampus,
  };
}

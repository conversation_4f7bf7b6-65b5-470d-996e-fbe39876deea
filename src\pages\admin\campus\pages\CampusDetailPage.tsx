import { Table } from "@/components/shared/table";
import { useTanStackTable } from "@/components/shared/table/custom/use-tan-stack-table";
import { TablePagination } from "@/components/shared/table/pagination";
import WidgetCard from "@/components/shared/widget-card";
import { Button } from "@/components/ui/button";
import type { Campus, Usina } from "@/types";
import {
  formatArea,
  formatCapacity,
  formatCoordinate,
  formatEfficiency,
} from "@/utils";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowLeft, Edit3, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { useCampus } from "../hooks/useCampus";
import { useUsinas } from "../hooks/useUsinas";

export default function CampusDetailPage() {
  const { id } = useParams<{ id: string }>();
  const { campus } = useCampus();
  const { usinas, loading: usinasLoading, deleteUsina } = useUsinas(id);
  const [currentCampus, setCurrentCampus] = useState<Campus | undefined>(
    undefined
  );
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    if (campus && id) {
      const foundCampus = campus.find((c) => c.id === id);
      setCurrentCampus(foundCampus);
    }
  }, [campus, id]);

  const handleDeleteUsina = async (usinaId: string) => {
    if (window.confirm("Tem certeza que deseja excluir esta usina?")) {
      setDeletingId(usinaId);
      try {
        await deleteUsina(usinaId);
      } catch (error) {
        console.error("Erro ao excluir usina:", error);
        alert("Erro ao excluir usina. Tente novamente.");
      } finally {
        setDeletingId(null);
      }
    }
  };

  const columns: ColumnDef<Usina>[] = [
    {
      accessorKey: "name",
      header: "Nome",
      cell: ({ getValue }) => (
        <span className="font-medium text-gray-900">
          {getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "capacity",
      header: "Capacidade",
      cell: ({ getValue }) => (
        <span className="text-gray-700">
          {formatCapacity(getValue() as number)}
        </span>
      ),
    },
    {
      accessorKey: "model",
      header: "Modelo",
      cell: ({ getValue }) => (
        <span className="text-gray-700">{getValue() as string}</span>
      ),
    },
    {
      accessorKey: "module_qtd",
      header: "Qtd. Módulos",
      cell: ({ getValue }) => (
        <span className="text-gray-700">{getValue() as number}</span>
      ),
    },
    {
      accessorKey: "eficiency",
      header: "Eficiência",
      cell: ({ getValue }) => (
        <span className="text-gray-700">
          {formatEfficiency(getValue() as number)}
        </span>
      ),
    },
    {
      accessorKey: "area_m2",
      header: "Área",
      cell: ({ getValue }) => (
        <span className="text-gray-700">
          {formatArea(getValue() as number)}
        </span>
      ),
    },
    {
      id: "actions",
      header: "Ações",
      cell: ({ row }) => {
        const usina = row.original;
        const isDeleting = deletingId === usina.id;

        return (
          <div className="flex items-center gap-2">
            <Link to={`/admin/campus/${id}/usina/edit/${usina.id}`}>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Edit3 className="h-4 w-4" />
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
              onClick={() => handleDeleteUsina(usina.id)}
              disabled={isDeleting}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const { table } = useTanStackTable({
    tableData: usinas || [],
    columnConfig: columns,
    options: {
      initialState: {
        pagination: {
          pageIndex: 0,
          pageSize: 10,
        },
      },
    },
  });

  if (!currentCampus) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Carregando campus...</div>
      </div>
    );
  }

  const totalCapacity =
    usinas?.reduce((sum, usina) => sum + usina.capacity, 0) || 0;
  const totalUsinas = usinas?.length || 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/admin/campus">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            {currentCampus.name}
          </h1>
          <p className="text-gray-600">
            {currentCampus.city} • {formatCoordinate(currentCampus.lat)},{" "}
            {formatCoordinate(currentCampus.long)}
          </p>
        </div>
      </div>

      {/* Resumo do Campus */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <WidgetCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {totalUsinas}
            </div>
            <div className="text-sm text-gray-600">Usinas</div>
          </div>
        </WidgetCard>

        <WidgetCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {formatCapacity(totalCapacity)}
            </div>
            <div className="text-sm text-gray-600">Capacidade Total</div>
          </div>
        </WidgetCard>

        <WidgetCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {currentCampus.status === "ACTIVE"
                ? "Ativo"
                : currentCampus.status === "INACTIVE"
                  ? "Inativo"
                  : "Manutenção"}
            </div>
            <div className="text-sm text-gray-600">Status</div>
          </div>
        </WidgetCard>
      </div>

      {/* Lista de Usinas */}
      <WidgetCard
        title="Usinas do Campus"
        action={
          <Link to={`/admin/campus/${id}/usina/create`}>
            <Button className="flex items-center gap-2" size="sm">
              <Plus className="h-4 w-4" />
              Nova Usina
            </Button>
          </Link>
        }
      >
        {usinasLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">Carregando usinas...</div>
          </div>
        ) : usinas && usinas.length > 0 ? (
          <>
            <Table data={usinas} columns={columns} />
            <TablePagination table={table} />
          </>
        ) : (
          <div className="text-center py-8">
            <div className="text-gray-500">Nenhuma usina encontrada</div>
            <Link to={`/admin/campus/${id}/usina/create`}>
              <Button className="mt-4 flex items-center gap-2" size="sm">
                <Plus className="h-4 w-4" />
                Adicionar Primeira Usina
              </Button>
            </Link>
          </div>
        )}
      </WidgetCard>
    </div>
  );
}

import WidgetCard from "@/components/shared/widget-card";
import { Button } from "@/components/ui/button";
import {
  downloadMigrationFile,
  exportFirebaseData,
  generateSQLScript
} from "@/lib/firebase-migration";
import { checkDataExists, clearFirebase, seedFirebase } from "@/lib/firebase-seeds";

import { AlertTriangle, Database, Download, FileText, Trash2, Zap } from "lucide-react";
import { useState } from "react";

export default function SeedManager() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info', text: string } | null>(null);

  const handleSeed = async () => {
    if (!window.confirm("Tem certeza que deseja popular o banco com dados unificados (todos os providers)? Esta ação pode sobrescrever dados existentes.")) {
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      // Verificar se já existem dados
      const status = await checkDataExists();
      if (status.hasData) {
        setMessage({ type: 'info', text: 'Dados já existem no banco!' });
        return;
      }

      const result = await seedFirebase();
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erro ao adicionar dados unificados' });
    } finally {
      setLoading(false);
    }
  };

  const handleClear = async () => {
    if (!window.confirm("⚠️ ATENÇÃO: Esta ação irá limpar TODOS os dados do banco. Tem certeza?")) {
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      const result = await clearFirebase();
      if (result.success) {
        setMessage({ type: 'info', text: result.message });
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erro ao limpar banco de dados' });
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    setLoading(true);
    setMessage(null);

    try {
      const data = await exportFirebaseData();
      downloadMigrationFile(data, `firebase-migration-${new Date().toISOString().split('T')[0]}.json`);
      setMessage({ type: 'success', text: `Exportação concluída: ${data.metadata.totalRecords} registros` });
    } catch (error) {
      setMessage({ type: 'error', text: 'Erro ao exportar dados' });
    } finally {
      setLoading(false);
    }
  };

  const handleExportSQL = async () => {
    setLoading(true);
    setMessage(null);

    try {
      const data = await exportFirebaseData();
      const sqlScript = generateSQLScript(data);

      // Criar e baixar arquivo SQL
      const blob = new Blob([sqlScript], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `firebase-migration-${new Date().toISOString().split('T')[0]}.sql`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setMessage({ type: 'success', text: `Script SQL gerado: ${data.metadata.totalRecords} registros` });
    } catch (error) {
      setMessage({ type: 'error', text: 'Erro ao gerar script SQL' });
    } finally {
      setLoading(false);
    }
  };



  return (
    <WidgetCard className="m-6">
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Database className="h-4 w-4" />
          <span>Gerencie os dados do emulador Firebase</span>
        </div>

        {/* Operações de Desenvolvimento */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">🛠️ Desenvolvimento Local</h3>
          <div className="flex flex-wrap gap-3">

            <Button
              onClick={handleSeed}
              disabled={loading}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <Zap className="h-4 w-4" />
              {loading ? "Populando..." : "Popular com Dados Unificados"}
            </Button>

            <Button
              onClick={handleClear}
              disabled={loading}
              variant="destructive"
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              {loading ? "Limpando..." : "Limpar Banco"}
            </Button>
          </div>
        </div>



        {/* Operações de Migração */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">🚀 Migração para Produção</h3>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={handleExport}
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {loading ? "Exportando..." : "Exportar JSON"}
            </Button>

            <Button
              onClick={handleExportSQL}
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              {loading ? "Gerando..." : "Gerar SQL"}
            </Button>
          </div>
        </div>

        {message && (
          <div className={`p-3 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
            message.type === 'error' ? 'bg-red-50 text-red-800 border border-red-200' :
              'bg-blue-50 text-blue-800 border border-blue-200'
            }`}>
            <div className="flex items-center gap-2">
              {message.type === 'success' && <Database className="h-4 w-4" />}
              {message.type === 'error' && <AlertTriangle className="h-4 w-4" />}
              {message.type === 'info' && <AlertTriangle className="h-4 w-4" />}
              <span>{message.text}</span>
            </div>
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p>• <strong>Popular Dados Unificados:</strong> Adiciona todos os providers, campus e usinas necessários</p>
          <p>• <strong>Limpar:</strong> Remove todos os dados do banco (apenas desenvolvimento)</p>

          <p>• <strong>Exportar JSON:</strong> Baixa dados para migração</p>
          <p>• <strong>Gerar SQL:</strong> Cria script SQL para outros bancos</p>
          <p>• Use apenas no emulador local para testes</p>
        </div>
      </div>
    </WidgetCard>
  );
}

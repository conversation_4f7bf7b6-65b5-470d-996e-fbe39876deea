import { GraphCumulativePoint } from "@/types";
import { cn } from "@/utils/merge-classes";
import { CustomTooltip } from "@shared/custom-tooltip";
import DropdownAction from "@shared/dropdown-action";
import SimpleBar from "@shared/simplebar";
import WidgetCard from "@shared/widget-card";
import { useState } from "react";
import {
  Bar,
  ComposedChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts";
// Removido Title do RizzUI - usando h3 nativo

const dataViews = [
  { value: "week", label: "Semana" },
  { value: "month", label: "Mê<PERSON>" },
  { value: "year", label: "Anual" },
];

const barSizeOptions = {
  week: 36,
  month: 18,
  year: 48,
};

const Description = ({ value }: { value: number }) => (
  <div className={cn("mt-1 space-y-3")}>
    <h4 className="text-gray-600">{value.toFixed(2)} kW</h4>
  </div>
);

interface BarChartProps {
  graphData: {
    week: GraphCumulativePoint[];
    month: GraphCumulativePoint[];
    year: GraphCumulativePoint[];
  };
  className?: string;
}

function BarChart({ graphData, className }: BarChartProps) {
  const [filteredData, setFilteredData] = useState(graphData.week);
  const [barSize, setBarSize] = useState(barSizeOptions.week);
  const [view, setView] = useState("week");

  const handleChange = (data: string) => {
    setFilteredData(graphData[data as keyof typeof graphData]);
    setBarSize(barSizeOptions[data as keyof typeof barSizeOptions]);
    setView(data);
  };

  function convertLabelX(dateString: string) {
    const date = new Date(dateString);
    if (view === "month") {
      return date.toLocaleDateString("pt-BR", {
        day: "2-digit",
      });
    }

    if (view === "year") {
      return date.toLocaleDateString("pt-BR", {
        month: "short",
      });
    }

    return date.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
    });
  }

  return (
    <WidgetCard
      title="Histórico de Geração"
      titleClassName="font-normal sm:text-sm"
      className={cn(
        "flex h-full flex-col @container [background:linear-gradient(29deg,#b1c1d1_12.96%,#f0f6ff_94.88%)]",
        className
      )}
      action={
        <DropdownAction
          prefixIconClassName="text-gray-600"
          selectClassName="hover:text-gray-600"
          className="rounded-md border hover:text-gray-600"
          options={dataViews}
          dropdownClassName="!z-0"
          onChange={handleChange}
        />
      }
      rounded="xl"
    >
      <div className="flex h-full flex-col justify-between">
        <Description
          value={filteredData?.reduce((acc, val) => acc + val.generation, 0)}
        />
        <SimpleBar>
          <div className="h-[20rem] w-full pt-9 @lg:pt-8">
            <ResponsiveContainer width="100%" height="100%" minWidth={400}>
              <ComposedChart
                data={filteredData}
                margin={{
                  left: 0,
                  top: 20,
                }}
                className="[&_.recharts-tooltip-cursor]:fill-opacity-20 dark:[&_.recharts-tooltip-cursor]:fill-opacity-10 [&_.recharts-cartesian-axis-tick-value]:fill-stone-800 [&_.recharts-cartesian-axis.xAxis]:translate-y-2 [&_.recharts-cartesian-axis.yAxis]:-translate-y-3 rtl:[&_.recharts-cartesian-axis.yAxis]:-translate-x-12 [&_.recharts-label-list]:-translate-y-1 [&_path.recharts-rectangle]:!stroke-none"
              >
                <XAxis
                  dataKey="label"
                  axisLine={false}
                  tickLine={false}
                  tickFormatter={(dateString) => convertLabelX(dateString)}
                />
                <Tooltip content={<CustomTooltip />} cursor={false} />
                <Bar
                  dataKey="generation"
                  fill="#F88B11"
                  barSize={barSize}
                  stroke="#FF7A2F"
                  label={{
                    position: "top",
                    fill: "text-slate-800",
                    fontSize: 12,
                  }}
                  radius={[4, 4, 0, 0]}
                  unit="kW"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </SimpleBar>
      </div>
    </WidgetCard>
  );
}

export default BarChart;

import { CustomTooltip } from "@/components/shared/custom-tooltip";
import TrendingUpIcon from "@/components/shared/icons/trending-up";
import SimpleBar from "@/components/shared/simplebar";
import WidgetCard from "@/components/shared/widget-card";
import { cn } from "@/utils/merge-classes";
import { useMemo } from "react";
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
// Removido Flex e Title do RizzUI - usando divs e h3 nativos

const COLORS = {
  revenue: { dark: "#28D775", light: "#28D775" },
  expenses: { dark: "#273849", light: "#111A23" },
};

const totalStatisticsData = [
  { label: "Jan", revenue: 20, expenses: 10 },
  { label: "Feb", revenue: 30, expenses: 15 },
  { label: "Mar", revenue: 40, expenses: 20 },
  { label: "Apr", revenue: 50, expenses: 25 },
  { label: "May", revenue: 60, expenses: 30 },
  { label: "Jun", revenue: 70, expenses: 35 },
  { label: "Jul", revenue: 80, expenses: 40 },
];

const totalStatisticsLegend = [{ name: "Revenue" }, { name: "Expenses" }];

const StatisticsSummary = () => (
  <div className="mb-3 mt-1 flex items-center gap-2 @[28rem]:mb-4">
    <h2 className="font-semibold">$83.45k</h2>
    <span className="flex items-center gap-1 text-green-dark">
      <TrendingUpIcon className="h-auto w-5" />
      <span className="font-medium leading-none"> +32.40%</span>
    </span>
  </div>
);

const ChartContainer = ({
  chartColors,
}: {
  chartColors: { revenue: string; expenses: string };
}) => (
  <SimpleBar className="-mb-3 pb-3">
    <div className="h-[24rem] w-full pt-6 @lg:pt-8">
      <ResponsiveContainer width="100%" height="100%" minWidth={600}>
        <ComposedChart
          barGap={8}
          data={totalStatisticsData}
          margin={{ left: -10, top: 20 }}
          className="[&_.recharts-tooltip-cursor]:fill-opacity-20 dark:[&_.recharts-tooltip-cursor]:fill-opacity-10 [&_.recharts-cartesian-axis-tick-value]:fill-gray-500 [&_.recharts-cartesian-axis.yAxis]:-translate-y-3 rtl:[&_.recharts-cartesian-axis.yAxis]:-translate-x-12 [&_.recharts-xAxis.xAxis]:translate-y-2.5 [&_path.recharts-rectangle]:!stroke-none"
        >
          <CartesianGrid
            vertical={false}
            strokeOpacity={0.435}
            strokeDasharray="8 10"
          />
          <XAxis dataKey="label" axisLine={false} tickLine={false} />
          <YAxis
            axisLine={false}
            tickLine={false}
            tickFormatter={(label) => `$${label}k`}
          />
          <Tooltip content={<CustomTooltip />} cursor={false} />
          <Bar
            dataKey="revenue"
            fill={chartColors.revenue}
            stroke={chartColors.revenue}
            barSize={28}
            radius={[4, 4, 0, 0]}
          />
          <Bar
            type="natural"
            dataKey="expenses"
            fill={chartColors.expenses}
            stroke={chartColors.expenses}
            barSize={28}
            radius={[4, 4, 0, 0]}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  </SimpleBar>
);

const CustomLegend = ({ className }: { className?: string }) => {
  const theme = "light";
  return (
    <div
      className={cn(
        "mt-2 flex flex-wrap items-start gap-3 lg:gap-7",
        className
      )}
    >
      {totalStatisticsLegend.map((item) => (
        <div key={item.name} className="flex items-center gap-1.5">
          <span
            className="-mt-0.5 h-3 w-3 rounded-full"
            style={{
              backgroundColor:
                COLORS[item.name.toLowerCase() as keyof typeof COLORS][
                  theme as "dark" | "light"
                ],
            }}
          />
          <span>{item.name}</span>
        </div>
      ))}
    </div>
  );
};

const TotalStatistics = ({ className }: { className?: string }) => {
  const theme = "light";

  const chartColors = useMemo(
    () => ({
      revenue:
        COLORS.revenue[theme as keyof typeof COLORS.revenue] ||
        COLORS.revenue.light,
      expenses:
        COLORS.expenses[theme as keyof typeof COLORS.expenses] ||
        COLORS.expenses.light,
    }),
    [theme]
  );

  return (
    <WidgetCard
      title="Total Statistics"
      titleClassName="text-gray-700 font-normal sm:text-sm"
      className={cn("min-h-[28rem] @container", className)}
      action={
        <div className="flex items-center gap-5">
          <CustomLegend className="hidden @[28rem]:mt-0 @[28rem]:inline-flex" />
        </div>
      }
      rounded="xl"
    >
      <StatisticsSummary />
      <CustomLegend className="mb-4 mt-0 inline-flex @[28rem]:hidden" />
      <ChartContainer chartColors={chartColors} />
    </WidgetCard>
  );
};

export default TotalStatistics;

import { Table } from "@/components/shared/table";
import { useTanStackTable } from "@/components/shared/table/custom/use-tan-stack-table";
import { TablePagination } from "@/components/shared/table/pagination";
import WidgetCard from "@/components/shared/widget-card";
import { But<PERSON> } from "@/components/ui/button";
import type { Provider } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { Pencil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useProviders } from "../hooks/useProviders";

export default function ProvidersListPage() {
  const { providers, loading, error, deleteProvider } = useProviders();
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDelete = async (id: string) => {
    if (window.confirm("Tem certeza que deseja excluir este provedor?")) {
      setDeletingId(id);
      try {
        await deleteProvider(id);
      } catch (error) {
        console.error("Erro ao excluir provedor:", error);
        alert("Erro ao excluir provedor. Tente novamente.");
      } finally {
        setDeletingId(null);
      }
    }
  };

  const columns: ColumnDef<Provider>[] = [
    {
      accessorKey: "name",
      header: "Nome",
      cell: ({ getValue }) => (
        <span className="font-medium text-gray-900">
          {getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "api_url",
      header: "API URL",
      cell: ({ getValue }) => {
        const url = getValue() as string;
        return (
          <a href={url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 hover:underline">
            {url}
          </a>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Criado em",
      cell: ({ getValue }) => {
        const date = new Date(getValue() as string);
        return (
          <span className="text-gray-600">
            {date.toLocaleDateString("pt-BR")}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Ações",
      cell: ({ row }) => {
        const provider = row.original;
        const isDeleting = deletingId === provider.id;

        return (
          <div className="flex items-center gap-2">
            <Link to={`/admin/providers/edit/${provider.id}`}>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Pencil className="h-4 w-4" />
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
              onClick={() => handleDelete(provider.id)}
              disabled={isDeleting}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const { table } = useTanStackTable({
    tableData: providers || [],
    columnConfig: columns,
    options: {
      initialState: {
        pagination: {
          pageIndex: 0,
          pageSize: 10,
        },
      },
    },
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Carregando provedores...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Erro ao carregar provedores: {error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Provedores</h1>
          <p className="text-gray-600">
            Gerencie os provedores de dados das usinas
          </p>
        </div>
        <Link to="/admin/providers/create">
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Novo Provedor
          </Button>
        </Link>
      </div>

      <WidgetCard>
        <Table data={providers || []} columns={columns} />
        <TablePagination table={table} />
      </WidgetCard>
    </div>
  );
}

import { cn } from "@/utils/merge-classes";
import { Info } from "lucide-react";
import { Button } from "../ui/button";

export default function CardData({
  value,
  unity,
  title,
  helper,
  img,
  color = "sky",
}: {
  value: string | number;
  unity: string;
  title?: string;
  helper?: string;
  img?: string;
  color?: string;
}) {
  const classes = {
    card: `
    flex justify-between align-center
    rounded-xl shadow-lg
    bg-position-[110%] bg-no-repeat
    px-4 py-2 2xl:py-3
    `,
    container: `h-fit rounded-xl bg-white`,
  };

  const bgGradient = {
    sky: "border-sky-100 bg-gradient-to-l from-sky-50 to-sky-100",
    yellow: "border-yellow-100 bg-gradient-to-l from-yellow-50 to-yellow-100",
    red: "border-red-100 bg-gradient-to-l from-red-50 to-red-100",
  };

  const helperText = helper && (
    <Button
      variant="ghost"
      size="icon"
      className="h-fit w-fit px-1 cursor-help"
      title={helper}
    >
      <Info className="w-[1.2rem]" />
    </Button>
  );

  const formatValue = (value: string | number, unity: string) => {
    if (unity === "R$") {
      return (
        <>
          <span className="text-3xl 2xl:text-4xl text-slate-500">{unity} </span>
          {value.toLocaleString("pt-BR")}
        </>
      );
    }
    return (
      <>
        {typeof value === "number" ? Number(value).toFixed(2) : value}{" "}
        <span className="text-3xl 2xl:text-4xl text-slate-500">{unity}</span>
      </>
    );
  };

  return (
    <div
      className={cn(
        "h-fit rounded-xl bg-white",
        bgGradient[color as keyof typeof bgGradient]
      )}
    >
      <div
        className={classes.card}
        style={img ? { backgroundImage: `url(/${img})` } : undefined}
      >
        <div className="flex flex-col shrink-0 gap-0.5">
          <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
            {title} {helperText}
          </span>
          <span className="font-data font-extrabold italic text-slate-600 text-4xl 2xl:text-5xl">
            {formatValue(value, unity)}
          </span>
        </div>
      </div>
    </div>
  );
}

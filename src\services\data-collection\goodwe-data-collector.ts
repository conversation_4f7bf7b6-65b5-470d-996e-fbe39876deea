import { APICallResult } from "@/types/services";
import { BaseDataCollector } from "./base-data-collector";

export class GoodweDataCollector extends BaseDataCollector {
  async collectUsinaData(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult[]> {
    const results: APICallResult[] = [];

    try {
      this.log(
        `Iniciando coleta de dados para usina ${usinaId} (Goodwe ID: ${providerUsinaId})`
      );

      // 1. Dados gerais da usina (detalhes do campus)
      const usinaGeral = await this.collectUsinaGeral(usinaId, providerUsinaId);
      if (usinaGeral) results.push(usinaGeral);

      // 2. Dados detalhados por inversor (coordenadas)
      const usinaDetalhes = await this.collectUsinaDetalhes(
        usinaId,
        providerUsinaId
      );
      if (usinaDetalhes) results.push(usinaDetalhes);

      // 3. Dados de warnings/alertas
      const warnings = await this.collectWarnings(usinaId, providerUsinaId);
      if (warnings) results.push(warnings);

      // 4. Dados ambientais
      const ambientais = await this.collectAmbientais(usinaId, providerUsinaId);
      if (ambientais) results.push(ambientais);

      // 5. Gráfico de energia
      const energyChart = await this.collectEnergyChart(usinaId, providerUsinaId);
      if (energyChart) results.push(energyChart);

      // 6. Histórico diário
      const dailyHistory = await this.collectDailyHistory(usinaId, providerUsinaId);
      if (dailyHistory) results.push(dailyHistory);

      this.log(
        `Coleta concluída para usina ${usinaId}: ${results.length} endpoints coletados`
      );
    } catch (error) {
      this.log(
        `Erro na coleta de dados para usina ${usinaId}: ${error}`,
        "error"
      );

      // Cria resultado de erro
      const errorResult = this.createAPICallResult(
        "error_job",
        usinaId,
        usinaId,
        "error",
        null,
        JSON.stringify({
          error: error instanceof Error ? error.message : String(error),
        }),
        false,
        error instanceof Error ? error.message : String(error)
      );

      results.push(errorResult);
    }

    return results;
  }

  private async collectUsinaGeral(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult | null> {
    try {
      const endpoint = "/api/v3/PowerStation/GetPlantDetailByPowerstationId";
      const response = await this.makeAPIRequest(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `powerStationId=${providerUsinaId}`, // Format correto: form-urlencoded
      });

      const rawResponse = await response.text();
      const data = JSON.parse(rawResponse);

      return this.createAPICallResult(
        "usina_geral_job",
        usinaId,
        usinaId,
        endpoint,
        data,
        rawResponse,
        true
      );
    } catch (error) {
      this.log(
        `Erro ao coletar dados gerais da usina ${usinaId}: ${error}`,
        "error"
      );
      return null;
    }
  }

  private async collectUsinaDetalhes(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult | null> {
    try {
      const endpoint = "/api/BigScreen/GetSinglePowerStation";
      const response = await this.makeAPIRequest(endpoint, {
        method: "POST",
        body: JSON.stringify({
          id: providerUsinaId,
        }),
      });

      const rawResponse = await response.text();
      const data = JSON.parse(rawResponse);

      return this.createAPICallResult(
        "usina_detalhes_job",
        usinaId,
        usinaId,
        endpoint,
        data,
        rawResponse,
        true
      );
    } catch (error) {
      this.log(
        `Erro ao coletar detalhes da usina ${usinaId}: ${error}`,
        "error"
      );
      return null;
    }
  }

  private async collectInverterData(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult[]> {
    const results: APICallResult[] = [];

    try {
      // 1. Lista de inversores
      const inverterListEndpoint = "/api/v3/PowerStation/GetInverterList";
      const inverterListResponse = await this.makeAPIRequest(
        inverterListEndpoint,
        {
          method: "POST",
          body: JSON.stringify({
            powerstation_id: providerUsinaId,
          }),
        }
      );

      const inverterListRaw = await inverterListResponse.text();
      const inverterListData = JSON.parse(inverterListRaw);

      results.push(
        this.createAPICallResult(
          "inverter_list_job",
          usinaId,
          usinaId,
          inverterListEndpoint,
          inverterListData,
          inverterListRaw,
          true
        )
      );

      // 2. Dados de cada inversor (se houver)
      if (
        inverterListData.data?.list &&
        Array.isArray(inverterListData.data.list)
      ) {
        for (const inverter of inverterListData.data.list) {
          try {
            const inverterDetailEndpoint =
              "/api/v3/PowerStation/GetInverterDetail";
            const inverterDetailResponse = await this.makeAPIRequest(
              inverterDetailEndpoint,
              {
                method: "POST",
                body: JSON.stringify({
                  powerstation_id: providerUsinaId,
                  inverter_id: inverter.inverter_id || inverter.id,
                }),
              }
            );

            const inverterDetailRaw = await inverterDetailResponse.text();
            const inverterDetailData = JSON.parse(inverterDetailRaw);

            results.push(
              this.createAPICallResult(
                "inverter_detail_job",
                usinaId,
                usinaId,
                inverterDetailEndpoint,
                inverterDetailData,
                inverterDetailRaw,
                true
              )
            );
          } catch (error) {
            this.log(
              `Erro ao coletar dados do inversor ${inverter.inverter_id}: ${error}`,
              "warn"
            );
          }
        }
      }
    } catch (error) {
      this.log(
        `Erro ao coletar dados dos inversores da usina ${usinaId}: ${error}`,
        "error"
      );
    }

    return results;
  }

  private async collectWarnings(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult | null> {
    try {
      const endpoint = "/api/warning/PowerstationWarningsQuery";
      const response = await this.makeAPIRequest(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `pw_id=${providerUsinaId}`, // Format correto: form-urlencoded
      });

      const rawResponse = await response.text();
      const data = JSON.parse(rawResponse);

      return this.createAPICallResult(
        "warnings_job",
        usinaId,
        usinaId,
        endpoint,
        data,
        rawResponse,
        true
      );
    } catch (error) {
      this.log(
        `Erro ao coletar warnings da usina ${usinaId}: ${error}`,
        "error"
      );
      return null;
    }
  }

  private async collectAmbientais(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult | null> {
    try {
      const endpoint = "/api/v2/Powerstation/GetEnvironmental";
      const response = await this.makeAPIRequest(endpoint, {
        method: "POST",
        body: JSON.stringify({
          powerStationId: providerUsinaId, // Nome correto do campo
        }),
      });

      const rawResponse = await response.text();
      const data = JSON.parse(rawResponse);

      return this.createAPICallResult(
        "ambientais_job",
        usinaId,
        usinaId,
        endpoint,
        data,
        rawResponse,
        true
      );
    } catch (error) {
      this.log(
        `Erro ao coletar dados ambientais da usina ${usinaId}: ${error}`,
        "error"
      );
      return null;
    }
  }

  private async collectEnergyChart(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult | null> {
    try {
      const endpoint = "/api/v2/Charts/GetPlantPowerChart";
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      
      const response = await this.makeAPIRequest(endpoint, {
        method: "POST",
        body: JSON.stringify({
          id: providerUsinaId,
          date: today,
          full_script: false
        }),
      });

      const rawResponse = await response.text();
      const data = JSON.parse(rawResponse);

      return this.createAPICallResult(
        "energy_chart_job",
        usinaId,
        usinaId,
        endpoint,
        data,
        rawResponse,
        true
      );
    } catch (error) {
      this.log(
        `Erro ao coletar gráfico de energia da usina ${usinaId}: ${error}`,
        "error"
      );
      return null;
    }
  }

  private async collectDailyHistory(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult | null> {
    try {
      const endpoint = "/api/v2/Charts/GetChartByPlant";
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      
      const response = await this.makeAPIRequest(endpoint, {
        method: "POST",
        body: JSON.stringify({
          id: providerUsinaId,
          date: today,
          range: 2, // Diário
          chartIndexId: "3",
          isDetailFull: ""
        }),
      });

      const rawResponse = await response.text();
      const data = JSON.parse(rawResponse);

      return this.createAPICallResult(
        "daily_history_job",
        usinaId,
        usinaId,
        endpoint,
        data,
        rawResponse,
        true
      );
    } catch (error) {
      this.log(
        `Erro ao coletar histórico diário da usina ${usinaId}: ${error}`,
        "error"
      );
      return null;
    }
  }
}

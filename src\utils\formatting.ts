/**
 * Utilitários de formatação para exibição de dados
 */

// Formatação de datas
export const formatDate = (date: string | Date, locale: string = 'pt-BR'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(locale);
};

export const formatDateTime = (date: string | Date, locale: string = 'pt-BR'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString(locale);
};

// Formatação de números
export const formatNumber = (value: number | null | undefined, decimals: number = 2): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return 'N/A';
  }
  return value.toFixed(decimals);
};

export const formatCurrency = (value: number, currency: string = 'BRL', locale: string = 'pt-BR'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
  }).format(value);
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${formatNumber(value, decimals)}%`;
};

// Formatação de coordenadas
export const formatCoordinate = (value: number | null | undefined, decimals: number = 6): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return 'N/A';
  }
  return formatNumber(value, decimals);
};

export const formatLatitude = (lat: number): string => {
  const direction = lat >= 0 ? 'N' : 'S';
  return `${formatCoordinate(Math.abs(lat))}° ${direction}`;
};

export const formatLongitude = (lng: number): string => {
  const direction = lng >= 0 ? 'E' : 'W';
  return `${formatCoordinate(Math.abs(lng))}° ${direction}`;
};

// Formatação de unidades de energia
export const formatPower = (value: number, unit: string = 'kW'): string => {
  return `${formatNumber(value)} ${unit}`;
};

export const formatEnergy = (value: number, unit: string = 'kWh'): string => {
  return `${formatNumber(value)} ${unit}`;
};

// Formatação de área
export const formatArea = (value: number, unit: string = 'm²'): string => {
  return `${formatNumber(value)} ${unit}`;
};

// Formatação de azimute
export const formatAzimuth = (value: number): string => {
  return `${formatNumber(value, 0)}°`;
};

// Formatação de eficiência
export const formatEfficiency = (value: number): string => {
  return formatPercentage(value);
};

// Formatação de capacidade
export const formatCapacity = (value: number): string => {
  if (value >= 1000) {
    return formatPower(value / 1000, 'MW');
  }
  return formatPower(value, 'kW');
};

// Formatação de URLs para exibição
export const formatUrlForDisplay = (url: string, maxLength: number = 50): string => {
  if (url.length <= maxLength) {
    return url;
  }
  return `${url.substring(0, maxLength - 3)}...`;
};

// Formatação de nomes para exibição
export const formatName = (name: string): string => {
  return name.trim();
};

// Formatação de status
export const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'ACTIVE': 'Ativo',
    'INACTIVE': 'Inativo',
    'MAINTENANCE': 'Manutenção',
  };

  return statusMap[status] || status;
};

// Formatação de texto truncado
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return `${text.substring(0, maxLength - 3)}...`;
};

// Formatação de números grandes
export const formatLargeNumber = (value: number): string => {
  if (value >= 1000000) {
    return `${formatNumber(value / 1000000, 1)}M`;
  }
  if (value >= 1000) {
    return `${formatNumber(value / 1000, 1)}K`;
  }
  return formatNumber(value, 0);
};

// Formatação de tempo relativo
export const formatRelativeTime = (date: string | Date): string => {
  const now = new Date();
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const diffInMs = now.getTime() - targetDate.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return 'Hoje';
  } else if (diffInDays === 1) {
    return 'Ontem';
  } else if (diffInDays < 7) {
    return `${diffInDays} dias atrás`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks} semana${weeks > 1 ? 's' : ''} atrás`;
  } else {
    return formatDate(targetDate);
  }
};

/**
 * <PERSON>ript para testar a construção de URLs
 */

import { BaseDataCollector } from './services/data-collection/base-data-collector';

// Classe de teste que estende BaseDataCollector
class TestDataCollector extends BaseDataCollector {
  async collectUsinaData(usinaId: string, providerUsinaId: string) {
    return [];
  }

  // Método público para testar construção de URL
  async testUrlConstruction(endpoint: string) {
    try {
      // Simular a construção da URL sem fazer a requisição real
      let baseUrl = this.baseUrl.endsWith("/") ? this.baseUrl.slice(0, -1) : this.baseUrl;
      let cleanEndpoint = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
      
      // Se a baseUrl já termina com /api e o endpoint começa com /api, remover duplicação
      if (baseUrl.endsWith("/api") && cleanEndpoint.startsWith("/api")) {
        cleanEndpoint = cleanEndpoint.substring(4); // Remove /api do início do endpoint
      }
      
      let url = `${baseUrl}${cleanEndpoint}`;
      
      console.log(`Base URL: ${this.baseUrl}`);
      console.log(`Endpoint: ${endpoint}`);
      console.log(`URL Final: ${url}`);
      console.log('---');
      
      return url;
    } catch (error) {
      console.error('Erro ao construir URL:', error);
      return null;
    }
  }
}

async function testUrlConstructions() {
  console.log('🧪 Testando construção de URLs...\n');
  
  // Cenários de teste
  const testCases = [
    {
      name: 'Base URL com /api (atual)',
      baseUrl: 'https://us.semsportal.com/api',
      endpoints: [
        '/api/v3/PowerStation/GetPlantDetailByPowerstationId',
        '/api/BigScreen/GetSinglePowerStation',
        '/api/warning/PowerstationWarningsQuery',
        '/api/v2/Charts/GetPlantPowerChart',
        '/api/v2/Charts/GetChartByPlant',
        '/api/v2/Powerstation/GetEnvironmental'
      ]
    },
    {
      name: 'Base URL sem /api (corrigida)',
      baseUrl: 'https://us.semsportal.com',
      endpoints: [
        '/api/v3/PowerStation/GetPlantDetailByPowerstationId',
        '/api/BigScreen/GetSinglePowerStation',
        '/api/warning/PowerstationWarningsQuery',
        '/api/v2/Charts/GetPlantPowerChart',
        '/api/v2/Charts/GetChartByPlant',
        '/api/v2/Powerstation/GetEnvironmental'
      ]
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}`);
    console.log(`Base URL: ${testCase.baseUrl}\n`);
    
    const collector = new TestDataCollector('test', 'fake-token', testCase.baseUrl);
    
    for (const endpoint of testCase.endpoints) {
      await collector.testUrlConstruction(endpoint);
    }
    
    console.log('\n');
  }
  
  console.log('✅ Teste concluído!');
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testUrlConstructions();
}

export { testUrlConstructions };

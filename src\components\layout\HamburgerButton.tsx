import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

interface HamburgerButtonProps {
  toggleAction: () => void;
  isOpen: boolean;
}

export default function HamburgerButton({
  toggleAction,
  isOpen,
}: HamburgerButtonProps) {
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => {
        toggleAction();
      }}
      className="relative z-50 w-11 h-11 rounded-full hover:bg-gray-100 focus:bg-gray-100 active:bg-gray-100 lg:hidden"
    >
      {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
    </Button>
  );
}

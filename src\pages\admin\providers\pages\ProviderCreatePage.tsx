import WidgetCard from "@/components/shared/widget-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type { CreateProviderData, ProviderFormErrors } from "@/types";
import { ArrowLeft, Check } from "lucide-react";
import { FormEvent, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useProviders } from "../hooks/useProviders";

export default function ProviderCreatePage() {
  const navigate = useNavigate();
  const { createProvider } = useProviders();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateProviderData>({
    name: "",
    api_url: "",
    status: "ACTIVE",
  });
  const [errors, setErrors] = useState<ProviderFormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: ProviderFormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome é obrigatório";
    }

    if (!formData.api_url.trim()) {
      newErrors.api_url = "URL é obrigatória";
    } else {
      // Validação básica de URL
      try {
        new URL(formData.api_url);
      } catch {
        newErrors.api_url = "URL inválida";
      }
    }



    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const created = await createProvider(formData);
      if (created?.id) {
        navigate(`/admin/providers/edit/${created.id}`);
      } else {
        navigate("/admin/providers");
      }
    } catch (error) {
      console.error("Erro ao criar provedor:", error);
      alert("Erro ao criar provedor. Tente novamente.");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    field: keyof CreateProviderData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/admin/providers">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            Novo Provedor
          </h1>
          <p className="text-gray-600">
            Adicione um novo provedor de dados das usinas
          </p>
        </div>
      </div>

      <WidgetCard>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Ex: WEG Solar"
                className="w-full"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API URL *
              </label>
              <Input
                type="url"
                value={formData.api_url}
                onChange={(e) => handleInputChange("api_url", e.target.value)}
                placeholder="https://exemplo.com/api"
                className="w-full"
              />
              {errors.api_url && (
                <p className="mt-1 text-sm text-red-600">{errors.api_url}</p>
              )}
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <div className="flex items-center gap-2">
                <span className={`text-sm ${formData.status === "INACTIVE" ? "text-gray-900" : "text-gray-500"}`}>Inativo</span>
                <Input
                  type="checkbox"
                  checked={formData.status === "ACTIVE"}
                  onChange={(e) => handleInputChange("status", e.target.checked ? "ACTIVE" : "INACTIVE")}
                  className="h-4 w-4"
                />
                <span className={`text-sm ${formData.status === "ACTIVE" ? "text-gray-900" : "text-gray-500"}`}>Ativo</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 pt-6 border-t border-gray-200">
            <Button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              {loading ? "Salvando..." : "Salvar Provedor"}
            </Button>
            <Link to="/admin/providers">
              <Button variant="outline" disabled={loading}>
                Cancelar
              </Button>
            </Link>
          </div>
        </form>
      </WidgetCard>
    </div>
  );
}

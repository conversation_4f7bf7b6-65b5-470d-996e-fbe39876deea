import { Table } from "@/components/shared/table";
import { useTanStackTable } from "@/components/shared/table/custom/use-tan-stack-table";
import { TablePagination } from "@/components/shared/table/pagination";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Search } from "lucide-react";
import * as React from "react";
import { useState } from "react";

// Exemplo de dados
interface ExampleData {
  id: string;
  name: string;
  email: string;
  status: "active" | "inactive";
  createdAt: string;
}

const sampleData: ExampleData[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    createdAt: "2024-01-15",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "inactive",
    createdAt: "2024-01-10",
  },
  // Adicione mais dados conforme necessário
];

export const TableExample = () => {
  const [globalFilter, setGlobalFilter] = useState("");

  // Definição das colunas
  const columns: ColumnDef<ExampleData>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium"
          >
            Nome
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => (
        <div className="text-muted-foreground">{row.getValue("email")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <div
            className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
              status === "active"
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
            }`}
          >
            {status === "active" ? "Ativo" : "Inativo"}
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium"
          >
            Data de Criação
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString("pt-BR")}</div>;
      },
    },
    {
      id: "actions",
      header: "Ações",
      cell: () => {
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              Editar
            </Button>
            <Button variant="ghost" size="sm" className="text-red-600">
              Excluir
            </Button>
          </div>
        );
      },
    },
  ];

  // Configuração da tabela
  const { table } = useTanStackTable({
    tableData: sampleData,
    columnConfig: columns,
    options: {
      initialState: {
        pagination: {
          pageIndex: 0,
          pageSize: 10,
        },
      },
      enableSorting: true,
      enableFiltering: true,
    },
  });

  // Aplicar filtro global
  React.useEffect(() => {
    table.setGlobalFilter(globalFilter);
  }, [globalFilter, table]);

  return (
    <div className="space-y-4">
      {/* Barra de pesquisa */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Pesquisar..."
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Tabela */}
      <div className="rounded-md border">
        <Table data={sampleData} columns={columns} />
        <TablePagination table={table} />
      </div>

      {/* Informações de debug (remover em produção) */}
      <div className="text-sm text-muted-foreground">
        <p>Total de linhas: {table.getFilteredRowModel().rows.length}</p>
        <p>Página atual: {table.getState().pagination.pageIndex + 1}</p>
        <p>Total de páginas: {table.getPageCount()}</p>
      </div>
    </div>
  );
};

export default TableExample;

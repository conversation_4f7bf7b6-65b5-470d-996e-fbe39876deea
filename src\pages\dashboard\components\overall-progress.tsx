import WidgetCard from "@/components/shared/widget-card";
import { cn } from "@/utils/merge-classes";
import { <PERSON>, Pie, Pie<PERSON><PERSON>, ResponsiveContainer } from "recharts";
// Removido Box e Text do RizzUI - usando divs e spans nativos

type OverAllProgressDataDataType = {
  name: string;
  color: string;
  percentage: number;
  count: number;
};

const overAllProgressData: OverAllProgressDataDataType[] = [
  { name: "Potência Atual", percentage: 80, color: "#FF712F", count: 80 },
  { name: "On going", percentage: 20, color: "#666666", count: 20 },
];

export default function OverallProgress({ className }: { className?: string }) {
  return (
    <WidgetCard
      title="Potência Atual"
      headerClassName="items-center"
      className={cn("@container dark:bg-gray-100/50", className)}
      action={<></>}
    >
      <div className="relative h-48 w-full translate-y-6 @sm:h-80">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart
            margin={{
              top: 40,
              right: 10,
            }}
            className="relative focus:[&_.recharts-sector]:outline-none"
          >
            <Pie
              label
              data={overAllProgressData}
              endAngle={-10}
              stroke="none"
              startAngle={190}
              paddingAngle={1}
              cornerRadius={12}
              dataKey="percentage"
              innerRadius={"85%"}
              outerRadius={"100%"}
            >
              {overAllProgressData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
          </PieChart>
        </ResponsiveContainer>

        <div className="absolute bottom-15 start-1/2 -translate-x-1/2 text-center @sm:bottom-28">
          <span className="text-2xl font-bold text-gray-800 @lg:text-4xl">
            523
          </span>
          <span className="font-medium">kWh</span>
        </div>
      </div>
    </WidgetCard>
  );
}

import PieChartWithNeedle from "@/pages/dashboard/components/pie-chart-with-needle";

export default function CardCurrentStats({
  currentValue,
  maxValue,
  title,
}: {
  currentValue: number;
  maxValue: number;
  title?: string;
}) {
  const unity = "kW";
  const classes = {
    container: `
    flex flex-col align-center
    rounded-xl shadow-lg
    bg-position-[110%] bg-no-repeat
    px-4 py-2 2xl:py-3
    backdrop-blur-sm
    h-fit
    `,
    card: `
    flex justify-around
    `,
  };

  return (
    <div className={classes.container}>
      <h4 className="text-center">{title}</h4>
      <PieChartWithNeedle
        value={currentValue}
        total={maxValue}
        className="h-40 -ml-4"
      />
      <div className={classes.card}>
        <div className="flex flex-col shrink-0 gap-0.5">
          <span className="font-display text-gray-500 text-sm 2xl:text-base">
            Potência Atual
          </span>
          <span className="font-data font-extrabold italic text-slate-600 text-4xl">
            {currentValue}{" "}
            <span className="text-2xl text-slate-500">{unity}</span>
          </span>
        </div>
        <div className="flex flex-col text-end shrink-0 gap-0.5">
          <span className="font-display text-gray-500 text-sm 2xl:text-base">
            Máxima do Dia
          </span>
          <span className="font-data font-extrabold italic text-slate-600 text-4xl">
            {maxValue} <span className="text-2xl text-slate-500">{unity}</span>
          </span>
        </div>
      </div>
    </div>
  );
}

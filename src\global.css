@config "../tailwind.config.js";

@import "tailwindcss";

@import "tw-animate-css";

/*---break--- */

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: "<PERSON><PERSON><PERSON>", "system-ui", "sans-serif";
  --font-heading: "Fira Sans Condensed";
  --font-display: "Signika", "system-ui", "sans-serif";
  --font-data: "Sofia Sans Extra Condensed";
}

:root {
  line-height: 1.5;
  font-weight: 400;
  --radius: 0.625rem;

  /* Cores principais - Azul vibrante como primary */
  --background: oklch(0.99 0.005 240);
  --foreground: oklch(0.15 0.02 240);
  --card: oklch(0.98 0.01 240);
  --card-foreground: oklch(0.15 0.02 240);
  --popover: oklch(0.98 0.01 240);
  --popover-foreground: oklch(0.15 0.02 240);

  /* Primary - Azul energético */
  --primary: oklch(0.55 0.18 240);
  --primary-foreground: oklch(0.98 0.01 240);
  --primary-dark: oklch(0.45 0.18 240);

  /* Secondary - Verde suave */
  --secondary: oklch(76.723% 0.09971 158.804);
  --secondary-foreground: oklch(0.25 0.05 160);

  /* Muted - Azul acinzentado suave */
  --muted: oklch(96.769% 0.01429 238.254);
  --muted-foreground: oklch(0.45 0.05 240);

  /* Accent - Laranja vibrante */
  --accent: oklch(0.88 0.12 50);
  --accent-foreground: oklch(0.25 0.08 50);

  /* Destructive - Vermelho vibrante */
  --destructive: oklch(0.6 0.22 25);
  --destructive-foreground: oklch(0.98 0.01 25);

  /* Bordas e inputs - Azul muito suave */
  --border: oklch(0.88 0.04 240);
  --input: oklch(0.94 0.02 240);
  --ring: oklch(0.55 0.18 240);

  /* Charts - Cores vibrantes e distintas */
  --chart-1: oklch(0.6 0.2 240);
  /* Azul */
  --chart-2: oklch(0.65 0.18 160);
  /* Verde */
  --chart-3: oklch(0.7 0.15 50);
  /* Laranja */
  --chart-4: oklch(0.6 0.2 300);
  /* Roxo */
  --chart-5: oklch(0.65 0.2 25);
  /* Vermelho */

  /* Sidebar - Tons azuis */
  --sidebar: oklch(0.96 0.02 240);
  --sidebar-foreground: oklch(0.2 0.03 240);
  --sidebar-primary: oklch(0.55 0.18 240);
  --sidebar-primary-foreground: oklch(0.98 0.01 240);
  --sidebar-accent: oklch(0.88 0.12 50);
  --sidebar-accent-foreground: oklch(0.25 0.08 50);
  --sidebar-border: oklch(0.88 0.04 240);
  --sidebar-ring: oklch(0.55 0.18 240);
}

/** Heading */
h1 {
  @apply font-display text-4xl xl:text-5xl font-semibold;
}

h2 {
  @apply font-heading text-3xl xl:text-4xl font-semibold italic;
}

h3 {
  @apply font-heading text-2xl xl:text-3xl font-semibold italic;
}

h4 {
  @apply font-heading xl:text-2xl font-semibold italic;
}

h5 {
  @apply font-display text-xl font-semibold;
}

h1 small,
h2 small,
h3 small,
h4 small,
h5 small {
  @apply text-sm font-light;
}

/* Header Styles */
header {
  @apply sticky flex-col top-0 z-[9999] flex items-center backdrop-blur-xl transition-all duration-300 p-2 md:px-4 lg:px-6 h-14 justify-between;
}

nav {
  @apply container flex relative max-w-[100%] justify-between items-center lg:py-1 mx-auto;
}

nav>div {
  @apply w-[50%];
}

.content {
  @apply flex w-full flex-col lg:ms-[148px] lg:w-[calc(100%-148px)] 2xl:ms-42 2xl:w-[calc(100%-168px)];
}

aside {
  @apply fixed hidden lg:block bottom-0 start-0 z-50 h-full w-[148px] border-e-2 border-gray-100 backdrop-grayscale-0 backdrop-blur-md 2xl:w-42;
}

main {
  @apply flex flex-grow flex-col py-4 px-6 xl:p-10;
}

.bg-card {
  background: linear-gradient(135deg, #E8EBF4 0%, #f8f9fd 50%, #E8EBF4 100%);
}

/** Gráficos */
.bar_charts {
  /* Usar classes utilitárias válidas do Tailwind e fallback para background customizado */
  @apply flex h-full flex-col bg-[#b1c1d1] text-[#f0f6ff] @container;
  background: linear-gradient(29deg, #0E1012 12.96%, #6C4F3E 94.88%);
}

/*---break--- */
@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/*---break--- */
.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --primary-dark: oklch(0.82 0.004 286.32);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

/*---break--- */
@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    margin: 0;
    min-width: 375px;
    min-height: 100vh;
    background-image: url("/bg_lux1.jpg");
    background-size: cover;
  }
}
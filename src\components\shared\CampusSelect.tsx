import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Dados temporá<PERSON> até a migração para Firebase
const mockCampus = [
  { id: "campus-feliz", name: "Campus Feliz", city: "Feliz" },
  { id: "campus-restinga", name: "Campus Restinga", city: "Restinga" },
  { id: "campus-vacaria", name: "Campus Vacaria", city: "Vacaria" },
];

interface CampusSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export default function CampusSelect({
  value,
  onChange,
  placeholder = "Selecione um campus",
  className,
  disabled = false,
}: CampusSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {mockCampus.map((c) => (
          <SelectItem key={c.id} value={c.id}>
            {c.name} - {c.city}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

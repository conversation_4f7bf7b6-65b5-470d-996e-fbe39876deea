import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { db } from '@/lib/firebase';
import { collection, getDocs } from 'firebase/firestore';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';

interface DataDebugInfo {
  providers: Record<string, unknown>[];
  campus: Record<string, unknown>[];
  usinas: Record<string, unknown>[];
  dataAccess: Record<string, unknown>[];
}

export default function DataDebugger() {
  const [data, setData] = useState<DataDebugInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const fetchData = async () => {
    setLoading(true);
    setErrors([]);

    try {
      const [providersSnapshot, campusSnapshot, usinasSnapshot, dataAccessSnapshot] = await Promise.all([
        getDocs(collection(db, 'providers')),
        getDocs(collection(db, 'campus')),
        getDocs(collection(db, 'usinas')),
        getDocs(collection(db, 'providersDataAccess'))
      ]);

      const providers = providersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const campus = campusSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const usinas = usinasSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const dataAccess = dataAccessSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      setData({ providers, campus, usinas, dataAccess });

      // Verificar problemas
      const newErrors: string[] = [];

      // Verificar usinas sem campus
      const usinasSemCampus = usinas.filter(u => !u.campusId);
      if (usinasSemCampus.length > 0) {
        newErrors.push(`${usinasSemCampus.length} usinas sem campus relacionado`);
      }

      // Verificar usinas sem provider
      const usinasSemProvider = usinas.filter(u => !u.providerId);
      if (usinasSemProvider.length > 0) {
        newErrors.push(`${usinasSemProvider.length} usinas sem provider relacionado`);
      }

      // Verificar campus sem usinas
      const campusSemUsinas = campus.filter(c =>
        !usinas.some(u => u.campusId === c.id)
      );
      if (campusSemUsinas.length > 0) {
        newErrors.push(`${campusSemUsinas.length} campus sem usinas relacionadas`);
      }

      setErrors(newErrors);

    } catch (error) {
      console.error('Erro ao buscar dados:', error);
      setErrors([`Erro ao buscar dados: ${error}`]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const campusData = () => {
    if (data) {
      return (
        <div className="mt-6 p-3 text-left">
          <h4 className="font-medium mb-3"><span className="not-italic">🏫</span> Detalhes dos Campus:</h4>
          <div className="space-y-2">
            {data.campus.map((campus) => {
              const usinasRelacionadas = data.usinas.filter(u => u.campusId === campus.id);
              return (
                <div key={campus.id} className="text-sm border-l-4 pl-3 py-1">
                  <div className="font-medium">{campus.name}</div>
                  <div className="text-xs text-gray-600">
                    ID: {campus.id}
                  </div>
                  <div className="text-xs text-gray-600">
                    Usinas: {usinasRelacionadas.length > 0 ? (
                      <span className="text-green-600">✓ {usinasRelacionadas.length} usina(s)</span>
                    ) : (
                      <span className="text-red-600">✗ NENHUMA USINA</span>
                    )}
                  </div>
                  {usinasRelacionadas.length > 0 && (
                    <div className="text-xs text-gray-500 mt-1 ml-2">
                      {usinasRelacionadas.map(u => u.name).join(', ')}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      );
    }
  }

  const usinasData = () => {
    if (data) {
      return (
        <div className="mt-6 p-3 text-left">
          <h4 className="font-medium mb-3"><span className="not-italic">📊</span> Detalhes das Usinas:</h4>
          <div className="space-y-2">
            {data.usinas.map((usina) => {
              const campusRelacionado = data.campus.find(c => c.id === usina.campusId);
              const providerRelacionado = data.providers.find(p => p.id === usina.providerId);

              return (
                <div key={usina.id} className="text-sm border-l-4 pl-3 py-1">
                  <div className="font-medium">{usina.name}</div>
                  <div className="grid grid-rows-2 gap-2 text-xs text-gray-600">
                    <div className="text-xs text-gray-500">
                      ID da Usina: {usina.id}
                    </div>
                    <div>
                      Campus: {usina.campusId ? (
                        campusRelacionado ? (
                          <span className="text-green-600">✓ {campusRelacionado.name}</span>
                        ) : (
                          <span className="text-yellow-600">⚠️ ID: {usina.campusId} (não encontrado)</span>
                        )
                      ) : (
                        <span className="text-red-600">✗ NÃO RELACIONADO</span>
                      )}
                    </div>
                    <div>
                      Provider: {usina.providerId ? (
                        providerRelacionado ? (
                          <span className="text-green-600">✓ {providerRelacionado.name}</span>
                        ) : (
                          <span className="text-yellow-600">⚠️ ID: {usina.providerId} (não encontrado)</span>
                        )
                      ) : (
                        <span className="text-red-600">✗ NÃO RELACIONADO</span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
  }

  const providersData = () => {
    if (data) {
      return (
        <div className="mt-6 p-3 text-left">
          <h4 className="font-medium mb-3"><span className="not-italic">🔌</span> Detalhes dos Providers:</h4>
          <div className="space-y-2">
            {data.providers.map((provider) => {
              const usinasRelacionadas = data.usinas.filter(u => u.providerId === provider.id);
              const dataAccess = data.dataAccess.find(d => d.provider_id === provider.id);
              return (
                <div key={provider.id} className="text-sm border-l-4 pl-3 py-1">
                  <div className="font-medium">{provider.name}</div>
                  <div className="text-xs text-gray-600">
                    ID: {provider.id}
                  </div>
                  <div className="text-xs text-gray-600">
                    Acesso: {dataAccess ? (
                      <span className="text-green-600">✓ Configurado</span>
                    ) : (
                      <span className="text-red-600">✗ NÃO CONFIGURADO</span>
                    )}
                  </div>
                  <div className="text-xs text-gray-600">
                    Usinas: {usinasRelacionadas.length > 0 ? (
                      <span className="text-green-600">✓ {usinasRelacionadas.length} usina(s)</span>
                    ) : (
                      <span className="text-red-600">✗ NENHUMA USINA</span>
                    )}
                  </div>
                  {usinasRelacionadas.length > 0 && (
                    <div className="text-xs text-gray-500 mt-1 ml-2">
                      {usinasRelacionadas.map(u => u.name).join(', ')}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      );
    }
  }

  if (!data) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔍 Debug de Dados
            <RefreshCw className="h-4 w-4 animate-spin" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>Carregando dados...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔍 Debug de Dados
          <Button onClick={fetchData} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Atualizando...' : 'Atualizar'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">

        {/* Resumo */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center p-3 border rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{data.providers.length}</div>
            <div className="text-sm text-gray-600">Providers / {data.dataAccess.length} Acessos</div>
            {/* Detalhes dos Providers */}
            {providersData()}
          </div>
          <div className="text-center p-3 border rounded-lg">
            <div className="text-2xl font-bold text-green-600">{data.campus.length}</div>
            <div className="text-sm text-gray-600">Campus</div>
            {/* Detalhes dos Campus */}
            {campusData()}
          </div>
          <div className="text-center p-3 border rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{data.usinas.length}</div>
            <div className="text-sm text-gray-600">Usinas</div>
            {/* Detalhes das Usinas */}
            {usinasData()}
          </div>
        </div>

        {/* Erros */}
        {errors.length > 0 && (
          <div className="border border-red-200 rounded-lg p-3 bg-red-50">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="font-medium text-red-800">Problemas Encontrados:</span>
            </div>
            <ul className="text-sm text-red-700 space-y-1">
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

      </CardContent>
    </Card>
  );
}

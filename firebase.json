{"firestore": {"database": "(default)", "location": "southamerica-east1", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"firestore": {"port": 8080}, "database": {"port": 9000}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true}}
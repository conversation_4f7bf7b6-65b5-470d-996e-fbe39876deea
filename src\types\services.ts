// Tipos para o sistema de scraping híbrido (Frontend + Backend Node.js)
export interface ProviderTokenConfig {
  id: string;
  providerId: string;
  providerSlug: string;
  baseUrl: string;
  providerAccess: ProviderDataAccessConfig[];
  enabled: boolean;
  lastTokenUpdate?: Date;
  nextTokenUpdate?: Date;
  status: "idle" | "running" | "error" | "success";
}

export interface ProviderDataAccessConfig {
  id: string;
  panelName: string;
  panelUrl: string;
  credentials: {
    username: string;
    password: string;
  };
  endpoints: {
    login: string;
    [key: string]: string;
  };
  headers?: Record<string, string>;
}

// Job para coleta de dados via API (executado no frontend)
export interface APIDataJob {
  id: string;
  campusId: string;
  usinaId: string;
  providerId: string;
  status: "pending" | "running" | "completed" | "failed";
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  dataCollected: number;
  logs: string[];
}

// Resultado da coleta via API
export interface APICallResult {
  id: string;
  jobId: string;
  campusId: string;
  usinaId: string;
  providerId: string;
  endpoint: string;
  timestamp: Date;
  data: any;
  rawResponse: string;
  success: boolean;
  error?: string;
}

// Métricas do sistema
export interface SystemMetrics {
  totalAPIDataJobs: number;
  successfulAPIDataJobs: number;
  failedAPIDataJobs: number;
  lastSuccessfulDataCollection?: Date;
  averageDataCollectionTime: number;
}

// Configuração para captura de token (usado pelo backend Node.js)
export interface TokenCaptureRequest {
  providerSlug: string;
  panelName: string;
  panelUrl: string;
  username: string;
  password: string;
}

// Resposta da captura de token (início do job)
export interface TokenCaptureResponse {
  success: boolean;
  jobId: string;
  message: string;
  status: "pending" | "running" | "completed" | "failed";
}

// Status detalhado do job de captura de token
export interface TokenCaptureJobStatus {
  id: string;
  status: "pending" | "running" | "completed" | "failed";
  progress: number;
  message: string;
  startedAt: string;
  completedAt?: string;
  result?: {
    success: boolean;
    token: string;
    cookies: string[];
    expiresAt?: string;
  };
  error?: string;
}

// Resposta completa do status do job
export interface TokenCaptureStatusResponse {
  success: boolean;
  status: TokenCaptureJobStatus;
}

// Resultado da captura de token (usado pelo job manager)
export interface TokenCaptureResult {
  id: string;
  jobId: string;
  providerId: string;
  providerDataAccessId?: string; // ID do ProviderDataAccess
  panelName: string;
  token: string;
  cookies?: string[];
  headers?: Record<string, string>;
  timestamp: Date;
  success: boolean;
  error?: string;
  expiresAt?: Date;
}

// Job para captura de token
export interface ProviderTokenJob {
  id: string;
  providerId: string;
  status: "pending" | "running" | "completed" | "failed";
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  tokensCaptured: number;
  logs: string[];
}

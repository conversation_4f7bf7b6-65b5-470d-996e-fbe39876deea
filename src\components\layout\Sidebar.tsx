import SimpleBar from "@/components/shared/simplebar";
import listaCampus from "@/mocks/lista_campus.json";
import { ListaCampus } from "@/types/lista_campus.types";
import { cn } from "@/utils/merge-classes";
import { Fragment } from "react";
import { NavLink as RouterNavLink } from "react-router-dom";
import Logo from "./Logo";

export default function Sidebar() {
  return (
    <aside>
      <div className="sticky top-0 z-40 px-5 py-2 shadow-sm h-14">
        <Logo />
      </div>

      <SimpleBar className="h-[calc(100%-80px)]">
        <div className="mt-4 pb-3 3xl:mt-6">
          {listaCampus.map((campus: ListaCampus) => {
            return (
              <Fragment key={campus.id}>
                <RouterNavLink
                  to={`/dashboard/${campus.slug}`}
                  className={({ isActive }) =>
                    cn(
                      "group relative ml-2 my-0.5 flex items-center justify-between rounded-l-md pl-2 py-2 font-medium capitalize lg:my-1 2xl:ml-2 2xl:my-2",
                      isActive
                        ? "bg-white text-gray-900 font-semibold"
                        : "text-gray-700 transition-colors duration-200 hover:bg-white hover:text-gray-900"
                    )
                  }
                >
                  <div className="flex items-center truncate">
                    <span className="truncate">{campus.name}</span>
                  </div>
                </RouterNavLink>
              </Fragment>
            );
          })}
        </div>
      </SimpleBar>
    </aside>
  );
}

import { db } from "@/lib/firebase";
import {
  APICallResult,
  APIDataJob,
  ProviderTokenConfig,
  ProviderTokenJob,
  TokenCaptureResult,
} from "@/types/services";
import {
  addDoc,
  collection,
  doc,
  limit as firestoreLimit,
  getDoc,
  getDocs,
  orderBy,
  query,
  setDoc,
  Timestamp,
  where,
  writeBatch,
} from "firebase/firestore";

export class FirebaseStorageService {
  private readonly providerTokenConfigsCollection = "provider_token_configs";
  private readonly providerTokenJobsCollection = "provider_token_jobs";
  private readonly tokenCaptureResultsCollection = "token_capture_results";
  private readonly apiDataJobsCollection = "api_data_jobs";
  private readonly apiCallResultsCollection = "api_call_results";

  // Helper para remover campos undefined (Firestore não aceita)
  private removeUndefinedFields(obj: Record<string, any>): Record<string, any> {
    const cleaned: Record<string, any> = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key] = value;
      }
    }
    return cleaned;
  }

  // Configurações de Token por Provider
  async saveProviderTokenConfig(config: ProviderTokenConfig): Promise<void> {
    const configRef = doc(db, this.providerTokenConfigsCollection, config.id);
    await setDoc(configRef, {
      ...config,
      lastTokenUpdate: config.lastTokenUpdate
        ? Timestamp.fromDate(config.lastTokenUpdate)
        : null,
      nextTokenUpdate: config.nextTokenUpdate
        ? Timestamp.fromDate(config.nextTokenUpdate)
        : null,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
  }

  async getProviderTokenConfig(
    id: string
  ): Promise<ProviderTokenConfig | null> {
    const configRef = doc(db, this.providerTokenConfigsCollection, id);
    const configSnap = await getDoc(configRef);

    if (!configSnap.exists()) {
      return null;
    }

    const data = configSnap.data();
    return {
      ...data,
      lastTokenUpdate: data.lastTokenUpdate?.toDate() || undefined,
      nextTokenUpdate: data.nextTokenUpdate?.toDate() || undefined,
    } as ProviderTokenConfig;
  }

  async updateProviderTokenConfig(
    id: string,
    updates: Partial<ProviderTokenConfig>
  ): Promise<void> {
    const configRef = doc(db, this.providerTokenConfigsCollection, id);
    await setDoc(
      configRef,
      {
        ...updates,
        lastTokenUpdate: updates.lastTokenUpdate
          ? Timestamp.fromDate(updates.lastTokenUpdate)
          : null,
        nextTokenUpdate: updates.nextTokenUpdate
          ? Timestamp.fromDate(updates.nextTokenUpdate)
          : null,
        updatedAt: Timestamp.now(),
      },
      { merge: true }
    );
  }

  // Jobs de Captura de Token
  async createProviderTokenJob(
    job: Omit<ProviderTokenJob, "id">
  ): Promise<string> {
    const jobRef = await addDoc(
      collection(db, this.providerTokenJobsCollection),
      {
        ...job,
        startedAt: job.startedAt ? Timestamp.fromDate(job.startedAt) : null,
        completedAt: job.completedAt
          ? Timestamp.fromDate(job.completedAt)
          : null,
        createdAt: Timestamp.now(),
      }
    );
    return jobRef.id;
  }

  async updateProviderTokenJob(
    jobId: string,
    updates: Partial<ProviderTokenJob>
  ): Promise<void> {
    const jobRef = doc(db, this.providerTokenJobsCollection, jobId);
    await setDoc(
      jobRef,
      {
        ...updates,
        startedAt: updates.startedAt
          ? Timestamp.fromDate(updates.startedAt)
          : null,
        completedAt: updates.completedAt
          ? Timestamp.fromDate(updates.completedAt)
          : null,
        updatedAt: Timestamp.now(),
      },
      { merge: true }
    );
  }

  async getRecentProviderTokenJobs(
    limit: number = 10
  ): Promise<ProviderTokenJob[]> {
    const jobsRef = collection(db, this.providerTokenJobsCollection);
    const q = query(
      jobsRef,
      orderBy("createdAt", "desc"),
      firestoreLimit(limit)
    );
    const jobsSnap = await getDocs(q);

    return jobsSnap.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        startedAt: data.startedAt?.toDate() || undefined,
        completedAt: data.completedAt?.toDate() || undefined,
      } as ProviderTokenJob;
    });
  }

  // Resultados de Captura de Token
  async saveTokenCaptureResults(results: TokenCaptureResult[]): Promise<void> {
    if (results.length === 0) return;

    const batch = writeBatch(db);

    results.forEach((result) => {
      const resultRef = doc(collection(db, this.tokenCaptureResultsCollection));
      const dataToSave = this.removeUndefinedFields({
        ...result,
        timestamp: Timestamp.fromDate(result.timestamp),
        expiresAt: result.expiresAt
          ? Timestamp.fromDate(result.expiresAt)
          : null,
        createdAt: Timestamp.now(),
      });
      batch.set(resultRef, dataToSave);
    });

    await batch.commit();
  }

  async getRecentTokenCaptureResults(
    providerId: string,
    limitCount: number = 10
  ): Promise<TokenCaptureResult[]> {
    const resultsRef = collection(db, this.tokenCaptureResultsCollection);
    const q = query(
      resultsRef,
      where("providerId", "==", providerId),
      orderBy("timestamp", "desc"),
      firestoreLimit(limitCount)
    );
    const resultsSnap = await getDocs(q);

    return resultsSnap.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        timestamp: data.timestamp?.toDate() || new Date(),
        expiresAt: data.expiresAt?.toDate() || undefined,
      } as TokenCaptureResult;
    });
  }

  // Busca resultados de captura de token por ProviderDataAccess
  async getRecentTokenCaptureResultsByDataAccess(
    providerDataAccessId: string,
    limitCount: number = 10
  ): Promise<TokenCaptureResult[]> {
    const resultsRef = collection(db, this.tokenCaptureResultsCollection);
    const q = query(
      resultsRef,
      where("providerDataAccessId", "==", providerDataAccessId),
      orderBy("timestamp", "desc"),
      firestoreLimit(limitCount)
    );
    const resultsSnap = await getDocs(q);

    return resultsSnap.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        timestamp: data.timestamp?.toDate() || new Date(),
        expiresAt: data.expiresAt?.toDate() || undefined,
      } as TokenCaptureResult;
    });
  }

  // Jobs de Coleta de Dados via API
  async createAPIDataJob(job: Omit<APIDataJob, "id">): Promise<string> {
    const jobRef = await addDoc(collection(db, this.apiDataJobsCollection), {
      ...job,
      startedAt: job.startedAt ? Timestamp.fromDate(job.startedAt) : null,
      completedAt: job.completedAt ? Timestamp.fromDate(job.completedAt) : null,
      createdAt: Timestamp.now(),
    });
    return jobRef.id;
  }

  async updateAPIDataJob(
    jobId: string,
    updates: Partial<APIDataJob>
  ): Promise<void> {
    const jobRef = doc(db, this.apiDataJobsCollection, jobId);
    await setDoc(
      jobRef,
      {
        ...updates,
        startedAt: updates.startedAt
          ? Timestamp.fromDate(updates.startedAt)
          : null,
        completedAt: updates.completedAt
          ? Timestamp.fromDate(updates.completedAt)
          : null,
        updatedAt: Timestamp.now(),
      },
      { merge: true }
    );
  }

  async getAPIDataJob(usinaId: string): Promise<APIDataJob | null> {
    const jobsRef = collection(db, this.apiDataJobsCollection);
    const q = query(
      jobsRef,
      where("usinaId", "==", usinaId),
      orderBy("createdAt", "desc"),
      firestoreLimit(1)
    );
    const jobsSnap = await getDocs(q);

    if (jobsSnap.empty) {
      return null;
    }

    const data = jobsSnap.docs[0].data();
    return {
      ...data,
      startedAt: data.startedAt?.toDate() || undefined,
      completedAt: data.completedAt?.toDate() || undefined,
    } as APIDataJob;
  }

  async getRecentAPIDataJobs(limit: number = 10): Promise<APIDataJob[]> {
    const jobsRef = collection(db, this.apiDataJobsCollection);
    const q = query(
      jobsRef,
      orderBy("createdAt", "desc"),
      firestoreLimit(limit)
    );
    const jobsSnap = await getDocs(q);

    return jobsSnap.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        startedAt: data.startedAt?.toDate() || undefined,
        completedAt: data.completedAt?.toDate() || undefined,
      } as APIDataJob;
    });
  }

  // Resultados de Chamadas API
  async saveAPICallResults(results: APICallResult[]): Promise<void> {
    if (results.length === 0) return;

    const batch = writeBatch(db);

    results.forEach((result) => {
      const resultRef = doc(collection(db, this.apiCallResultsCollection));
      const dataToSave = this.removeUndefinedFields({
        ...result,
        timestamp: Timestamp.fromDate(result.timestamp),
        createdAt: Timestamp.now(),
      });
      batch.set(resultRef, dataToSave);
    });

    await batch.commit();
  }

  async getRecentAPICallResults(
    usinaId: string,
    limitCount: number = 10
  ): Promise<APICallResult[]> {
    const resultsRef = collection(db, this.apiCallResultsCollection);
    const q = query(
      resultsRef,
      where("usinaId", "==", usinaId),
      orderBy("timestamp", "desc"),
      firestoreLimit(limitCount)
    );
    const resultsSnap = await getDocs(q);

    return resultsSnap.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        timestamp: data.timestamp?.toDate() || new Date(),
      } as APICallResult;
    });
  }

  // ===== MÉTODOS DE LIMPEZA =====

  // Limpeza de dados antigos
  async cleanupOldData(daysToKeep: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    const cutoffTimestamp = Timestamp.fromDate(cutoffDate);

    // Limpa resultados antigos
    const resultsRef = collection(db, this.tokenCaptureResultsCollection);
    const oldResultsQuery = query(
      resultsRef,
      where("timestamp", "<", cutoffTimestamp)
    );
    const oldResultsSnap = await getDocs(oldResultsQuery);

    const batch = writeBatch(db);
    oldResultsSnap.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    // Limpa jobs antigos
    const jobsRef = collection(db, this.providerTokenJobsCollection);
    const oldJobsQuery = query(
      jobsRef,
      where("createdAt", "<", cutoffTimestamp)
    );
    const oldJobsSnap = await getDocs(oldJobsQuery);

    oldJobsSnap.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    // Limpa sessões expiradas
    const sessionsRef = collection(db, this.tokenCaptureResultsCollection);
    const expiredSessionsQuery = query(
      sessionsRef,
      where("expiresAt", "<", Timestamp.now())
    );
    const expiredSessionsSnap = await getDocs(expiredSessionsQuery);

    expiredSessionsSnap.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
  }
}

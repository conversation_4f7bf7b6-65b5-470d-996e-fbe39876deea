/**
 * Script de teste para verificar a coleta de dados do Goodwe
 * Execute este script para testar as correções implementadas
 */

import { ScrapingJobManager } from './services/job-manager';
import { FirebaseStorageService } from './services/firebase-storage';

async function testDataCollection() {
  console.log('🧪 Iniciando teste de coleta de dados...');
  
  try {
    const jobManager = new ScrapingJobManager();
    const storage = new FirebaseStorageService();
    
    // 1. Verificar se há tokens válidos
    console.log('\n📋 1. Verificando tokens disponíveis...');
    const recentTokens = await storage.getRecentTokenCaptureResults(5);
    console.log(`Tokens encontrados: ${recentTokens.length}`);
    
    recentTokens.forEach((token, index) => {
      console.log(`  ${index + 1}. Provider: ${token.providerId}, Success: ${token.success}, Expires: ${token.expiresAt}`);
    });
    
    // 2. Verificar configurações de providers
    console.log('\n🔧 2. Verificando configurações de providers...');
    const providerConfigs = await jobManager['getProviderTokenConfigs']();
    console.log(`Configurações encontradas: ${providerConfigs.length}`);
    
    providerConfigs.forEach((config, index) => {
      console.log(`  ${index + 1}. Provider: ${config.providerSlug}, ID: ${config.providerId}, Base URL: ${config.baseUrl}`);
    });
    
    // 3. Verificar usinas
    console.log('\n🏭 3. Verificando usinas...');
    const dataJobs = await jobManager['getDataCollectionJobs']();
    console.log(`Usinas encontradas: ${dataJobs.length}`);
    
    dataJobs.forEach((job, index) => {
      console.log(`  ${index + 1}. Usina: ${job.usinaId}, Provider: ${job.providerId}, Campus: ${job.campusId}`);
    });
    
    // 4. Testar busca de token para um provider específico
    if (providerConfigs.length > 0) {
      console.log('\n🔑 4. Testando busca de token...');
      const firstConfig = providerConfigs[0];
      const token = await jobManager['getProviderAPIToken'](firstConfig.id);
      console.log(`Token encontrado para ${firstConfig.id}: ${token ? 'SIM' : 'NÃO'}`);
      if (token) {
        console.log(`Token (primeiros 20 chars): ${token.substring(0, 20)}...`);
      }
    }
    
    // 5. Testar coleta para uma usina (apenas simulação)
    if (dataJobs.length > 0) {
      console.log('\n🚀 5. Simulando coleta para primeira usina...');
      const firstJob = dataJobs[0];
      console.log(`Usina selecionada: ${firstJob.usinaId}`);
      console.log(`Provider: ${firstJob.providerId}`);
      
      // Não executar a coleta real, apenas mostrar que chegaria até aqui
      console.log('✅ Simulação concluída - coleta não executada para evitar sobrecarga');
    }
    
    console.log('\n🎉 Teste concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testDataCollection();
}

export { testDataCollection };

// Configurações do Firebase para diferentes ambientes
export interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId?: string;
}

// Configuração de desenvolvimento (emulador)
export const devConfig: FirebaseConfig = {
  apiKey: "dev-api-key",
  authDomain: "dev-project.firebaseapp.com",
  projectId: "dev-project",
  storageBucket: "dev-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:dev123",
};

// Configuração de staging
export const stagingConfig: FirebaseConfig = {
  apiKey: import.meta.env.VITE_STAGING_FIREBASE_API_KEY || "",
  authDomain: import.meta.env.VITE_STAGING_FIREBASE_AUTH_DOMAIN || "",
  projectId: import.meta.env.VITE_STAGING_FIREBASE_PROJECT_ID || "",
  storageBucket: import.meta.env.VITE_STAGING_FIREBASE_STORAGE_BUCKET || "",
  messagingSenderId: import.meta.env.VITE_STAGING_FIREBASE_MESSAGING_SENDER_ID || "",
  appId: import.meta.env.VITE_STAGING_FIREBASE_APP_ID || "",
  measurementId: import.meta.env.VITE_STAGING_FIREBASE_MEASUREMENT_ID || "",
};

// Configuração de produção
export const productionConfig: FirebaseConfig = {
  apiKey: import.meta.env.VITE_PROD_FIREBASE_API_KEY || "",
  authDomain: import.meta.env.VITE_PROD_FIREBASE_AUTH_DOMAIN || "",
  projectId: import.meta.env.VITE_PROD_FIREBASE_PROJECT_ID || "",
  storageBucket: import.meta.env.VITE_PROD_FIREBASE_STORAGE_BUCKET || "",
  messagingSenderId: import.meta.env.VITE_PROD_FIREBASE_MESSAGING_SENDER_ID || "",
  appId: import.meta.env.VITE_PROD_FIREBASE_APP_ID || "",
  measurementId: import.meta.env.VITE_PROD_FIREBASE_MEASUREMENT_ID || "",
};

// Função para obter configuração baseada no ambiente
export function getFirebaseConfig(): FirebaseConfig {
  const env = import.meta.env.MODE;

  switch (env) {
    case 'development':
      return devConfig;
    case 'staging':
      return stagingConfig;
    case 'production':
      return productionConfig;
    default:
      console.warn(`Ambiente desconhecido: ${env}, usando desenvolvimento`);
      return devConfig;
  }
}

// Função para verificar se está usando emulador
export function isUsingEmulator(): boolean {
  return import.meta.env.MODE === 'development';
}

// Função para obter nome do ambiente
export function getEnvironmentName(): string {
  const env = import.meta.env.MODE;

  switch (env) {
    case 'development':
      return '🟢 Desenvolvimento (Emulador)';
    case 'staging':
      return '🟡 Staging';
    case 'production':
      return '🔴 Produção';
    default:
      return '❓ Desconhecido';
  }
}

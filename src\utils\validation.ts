/**
 * Utilitários de validação para formulários
 */

// Validação de URL
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Validação de email
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validação de coordenadas (latitude)
export const isValidLatitude = (lat: number): boolean => {
  return lat >= -90 && lat <= 90;
};

// Validação de coordenadas (longitude)
export const isValidLongitude = (lng: number): boolean => {
  return lng >= -180 && lng <= 180;
};

// Validação de string não vazia
export const isNotEmpty = (value: string): boolean => {
  return value.trim().length > 0;
};

// Validação de número positivo
export const isPositiveNumber = (value: number): boolean => {
  return value > 0;
};

// Validação de número não negativo
export const isNonNegativeNumber = (value: number): boolean => {
  return value >= 0;
};

// Validação de range de números
export const isInRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max;
};

// Validação de string com tamanho mínimo
export const hasMinLength = (value: string, minLength: number): boolean => {
  return value.trim().length >= minLength;
};

// Validação de string com tamanho máximo
export const hasMaxLength = (value: string, maxLength: number): boolean => {
  return value.trim().length <= maxLength;
};

// Validação de campo obrigatório
export const isRequired = (value: any): boolean => {
  if (typeof value === 'string') {
    return isNotEmpty(value);
  }
  if (typeof value === 'number') {
    return !isNaN(value);
  }
  return value !== null && value !== undefined;
};

// Validação de azimute (0-360 graus)
export const isValidAzimuth = (azimuth: number): boolean => {
  return isInRange(azimuth, 0, 360);
};

// Validação de eficiência (0-100%)
export const isValidEfficiency = (efficiency: number): boolean => {
  return isInRange(efficiency, 0, 100);
};

// Validação de capacidade (deve ser positiva)
export const isValidCapacity = (capacity: number): boolean => {
  return isPositiveNumber(capacity);
};

// Validação de área (deve ser positiva)
export const isValidArea = (area: number): boolean => {
  return isPositiveNumber(area);
};

// Validação de quantidade de modelos (deve ser inteiro positivo)
export const isValidModelQuantity = (quantity: number): boolean => {
  return Number.isInteger(quantity) && isPositiveNumber(quantity);
};

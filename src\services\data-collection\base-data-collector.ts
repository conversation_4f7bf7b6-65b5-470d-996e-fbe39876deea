import { APICallResult } from "@/types/services";

export abstract class BaseDataCollector {
  protected providerId: string;
  protected apiToken: string;
  protected baseUrl: string;

  constructor(providerId: string, apiToken: string, baseUrl: string) {
    this.providerId = providerId;
    this.apiToken = apiToken;
    this.baseUrl = baseUrl;
  }

  abstract collectUsinaData(
    usinaId: string,
    providerUsinaId: string
  ): Promise<APICallResult[]>;

  protected async makeAPIRequest(
    endpoint: string,
    options: {
      method?: string;
      body?: string;
      headers?: Record<string, string>;
    } = {},
    params?: Record<string, string>
  ): Promise<Response> {
    let url = `${this.baseUrl}${endpoint}`;

    if (params) {
      const searchParams = new URLSearchParams(params);
      url += `?${searchParams.toString()}`;
    }

    const headers: Record<string, string> = {
      token: this.apiToken, // Goodwe usa header "token" em vez de "Authorization"
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    if (options.headers) {
      Object.assign(headers, options.headers);
    }

    console.log(`🌐 Fazendo requisição para: ${url}`);
    console.log(
      `🔑 Token: ${this.apiToken ? this.apiToken.substring(0, 20) + "..." : "AUSENTE"}`
    );
    console.log(`📋 Headers:`, headers);
    console.log(`📦 Body:`, options.body);

    const response = await fetch(url, {
      method: options.method || "GET",
      body: options.body,
      headers,
    });

    console.log(`📊 Resposta: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  }

  protected log(
    message: string,
    level: "info" | "warn" | "error" = "info"
  ): void {
    const timestamp = new Date().toISOString();
    console.log(
      `[${timestamp}] [${level.toUpperCase()}] [API-${this.providerId}] ${message}`
    );
  }

  protected async retry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        this.log(`Tentativa ${attempt} falhou: ${error}`, "warn");

        if (attempt < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError!;
  }

  protected createAPICallResult(
    jobId: string,
    campusId: string,
    usinaId: string,
    endpoint: string,
    data: Record<string, unknown> | null,
    rawResponse: string,
    success: boolean,
    error?: string
  ): APICallResult {
    return {
      id: `${this.providerId}_${usinaId}_${endpoint}_${Date.now()}`,
      jobId,
      campusId,
      usinaId,
      providerId: this.providerId,
      endpoint,
      timestamp: new Date(),
      data,
      rawResponse,
      success,
      error,
    };
  }
}

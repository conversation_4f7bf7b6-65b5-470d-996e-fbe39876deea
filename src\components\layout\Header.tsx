import { Button } from "@/components/ui/button";
import { useIsMounted } from "@/hooks/useIsMounted";
import { Bell, Settings } from "lucide-react";
import { useState } from "react";
import { useWindowScroll } from "react-use";
import HamburgerButton from "./HamburgerButton";
import Logo from "./Logo";

const offset = 2;

const menuItems = [
  { label: "Home", href: "/" },
  { label: "Campus", href: "/campus" },
  { label: "FAQ", href: "/faq" },
];

export default function Header() {
  const isMounted = useIsMounted();
  const windowScroll = useWindowScroll();
  const scroll = ((isMounted && windowScroll.y) as number) > offset;
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className={scroll ? "shadow-md" : "shadow-sm"}>
      <nav>
        {/* Left content */}
        <div className="flex items-center justify-between">
          {/* Mobile Menu Button */}
          <HamburgerButton
            toggleAction={toggleMobileMenu}
            isOpen={isMobileMenuOpen}
          />

          {/* Mobile Logo */}
          <Logo className="lg:hidden" />
        </div>

        {/* Right Buttons */}
        <div className="flex justify-end gap-4">
          <Button
            variant="ghost"
            size="icon"
            aria-label="Notification"
            className="action-icon"
          >
            <Bell className="w-auto h-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            aria-label="Settings"
            className="action-icon"
          >
            <Settings className="h-[22px] w-auto animate-spin-slow" />
          </Button>
        </div>
      </nav>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="px-4 py-2 backdrop-blur-md md:hidden bg-background/90">
          {menuItems.map((item) => (
            <a
              key={item.label}
              href={item.href}
              className="block transition-colors duration-200 hover:text-primary"
            >
              {item.label}
            </a>
          ))}
        </div>
      )}
    </header>
  );
}

{"language": "en", "function": null, "hasError": false, "msg": "success", "code": "0", "data": {"info": {"powerstation_id": "4c793d22-fa84-4e2e-940b-7ca46698ee4e", "time": "05/01/2025 17:25:00", "date_format_ym": "MM.yyyy", "stationname": "IFRS VACARIA", "address": "Estrada - <PERSON><PERSON><PERSON><PERSON> Oliveira - Distrito Industrial Iii, Vacaria - RS, 95200-000, Brazil", "battery_capacity": 0.0, "create_time": "06/16/2021 20:00:15", "capacity": 36.96, "powerstation_type": "<PERSON><PERSON><PERSON>", "status": 1, "is_stored": false, "only_bps": false, "only_bpu": false, "time_span": 3.0, "org_code": "DG02993000", "org_name": "Moove Energia Solar Ltda", "local_date": "2025-05-01 17:25:00"}, "kpi": {"month_generation": 141.1, "pac": 541.0, "power": 141.1, "total_power": 167215.8, "day_income": 126.99, "total_income": 150494.22, "yield_rate": 0.9, "currency": "USD"}, "isEvcharge": false, "isTigo": false, "isPowerflow": false, "isSec": false, "isGenset": false, "isMicroInverter": false, "hasLayout": false, "layout_id": "", "isMeter": false, "isEnvironmental": true, "powercontrol_status": 0, "chartsTypesByPlant": [{"date": "2025-05-02", "typeName": "PowerRadiation", "chartIndices": [{"indexName": "ONE_POWER_RADIATION", "indexLabel": "PowerRadiation", "chartIndexId": "2", "dateRange": [{"text": "Today", "value": "DAY", "type": "2", "now": "2025-05-01", "dateFormater": null}]}]}, {"date": "2025-05-02", "typeName": "Generation&Income", "chartIndices": [{"indexName": "TWO_YIELD_REVENUE", "indexLabel": "Generation&Income", "chartIndexId": "3", "dateRange": [{"text": "Day", "value": "DAY", "type": "2", "now": "2025-05-01", "dateFormater": null}, {"text": "Month", "value": "MONTH", "type": "3", "now": "2025-05", "dateFormater": null}, {"text": "Year", "value": "YEAR", "type": "4", "now": "2025", "dateFormater": null}]}, {"indexName": "TWO_YIELD_RADIATION", "indexLabel": "Radiation(MJ/㎡)", "chartIndexId": "4", "dateRange": [{"text": "Day", "value": "DAY", "type": "2", "now": "2025-05-01", "dateFormater": null}, {"text": "Month", "value": "MONTH", "type": "3", "now": "2025-05", "dateFormater": null}, {"text": "Year", "value": "YEAR", "type": "4", "now": "2025", "dateFormater": null}]}]}], "soc": [], "industrySoc": [], "isSec1000EtPlant": false}, "components": {"para": "{\"model\":{\"PowerStationId\":\"4c793d22-fa84-4e2e-940b-7ca46698ee4e\"}}", "langVer": 273, "timeSpan": 23, "api": "http://us.semsportal.com:82/api/v3/PowerStation/GetPlantDetailByPowerstationId", "msgSocketAdr": null}}
{"hasError": false, "code": 0, "msg": "Sucesso", "data": {"powerstation_id": "4c793d22-fa84-4e2e-940b-7ca46698ee4e", "stationname": "IFRS VACARIA", "powerstation_type": "<PERSON><PERSON><PERSON>", "capacity": 36.96, "address": "Estrada - <PERSON><PERSON> <PERSON><PERSON><PERSON> Oliveira - Distrito Industrial Iii, Vacaria - RS, 95200-000, Brazil Vacaria,Rio Grande do Sul,Brazil", "turnon_time": "06/16/2021", "synopsis_content": "Planta de geração fotovoltaica distribuída refere-se ao modo de operação de “construção e operação no local do usuário ou próximo, e implementação de“ auto-uso, excesso de energia à rede, consumo próximo e ajuste da rede elétrica ”no lado do usuário e na distribuição sistema de rede, as características das instalações de geração de energia fotovoltaica são equilibradas e ajustadas. A geração de energia fotovoltaica distribuída segue o princípio de se adaptar às condições locais, limpeza e altamente eficiente, disposição descentralizada e utilização de proximidade, aproveitando ao máximo os recursos locais de energia solar para substituir e reduzir o consumo de energia fóssil. Sem o uso de combustível, o custo operacional é muito baixo; não há partes móveis, não é fácil de danificar, a manutenção é simples, especialmente adequada para uso em condições autônomas; não produzirá nenhum desperdício, nenhuma poluição, ruído e outros riscos públicos, nenhum impacto adverso sobre o meio ambiente. Benefícios ambientais destacáveis. A geração de energia fotovoltaica distribuída é um novo tipo de geração de energia e modo de utilização abrangente de energia com amplas perspectivas de desenvolvimento. Ele pode realizar o fornecimento de energia nas proximidades sem transmissão de longa distância e evitar a perda de linhas de transmissão de longa distância.", "images": [], "yield_rate": 0.9, "currency": "USD", "inverters": [{"sn": "9030KMTU209W0202", "modelType": "GW30K-MT", "deviceName": "Inv. 30KW", "status": 1, "status_text": "Trabalhando"}], "lng": "-50.953819199999991", "lat": "-28.4553184"}, "components": {"para": null, "langVer": 273, "timeSpan": 0, "api": "http://us.semsportal.com:82/api/BigScreen/GetSinglePowerStation", "msgSocketAdr": ""}}
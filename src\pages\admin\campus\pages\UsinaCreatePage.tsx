import ProviderSelect from "@/components/shared/ProviderSelect";
import WidgetCard from "@/components/shared/widget-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import type { Campus, CreateUsinaData, ProviderDataAccess, UsinaFormErrors } from "@/types";
import {
  parseInteger,
  parseNumber,
  validateCapacity,
  validateRequiredFields,
} from "@/utils";
import { ArrowLeft, Check } from "lucide-react";
import { FormEvent, useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useProviderAccess } from "../../providers/hooks/useProviderAccess";
import { useCampus } from "../hooks/useCampus";
import { useUsinas } from "../hooks/useUsinas";

export default function UsinaCreatePage() {
  const { campusId } = useParams<{ campusId: string }>();
  const navigate = useNavigate();
  const { createUsina } = useUsinas();
  const { campus } = useCampus();
  const [loading, setLoading] = useState(false);
  const [currentCampus, setCurrentCampus] = useState<Campus | undefined>(
    undefined
  );
  const [formData, setFormData] = useState<CreateUsinaData>({
    name: "",
    capacity: 0,
    lat: 0,
    long: 0,
    model: "",
    module_qtd: 0,
    eficiency: 0,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "",
    campusId: campusId || "",
    providerId: "",
    provider_access: "default",
    status: "ACTIVE",
  });
  const { accessList } = useProviderAccess(formData.providerId);
  const [errors, setErrors] = useState<UsinaFormErrors>({});

  useEffect(() => {
    if (campus && campusId) {
      const foundCampus = campus.find((c) => c.id === campusId);
      if (foundCampus) {
        setCurrentCampus(foundCampus);
        // Pré-preencher coordenadas com as do campus
        setFormData((prev) => ({
          ...prev,
          lat: foundCampus.lat,
          long: foundCampus.long,
        }));
      }
    }
  }, [campus, campusId]);

  const validateForm = (): boolean => {
    const newErrors: UsinaFormErrors = {};

    // Validar campos obrigatórios
    const requiredErrors = validateRequiredFields(formData, [
      "name",
      "capacity",
      "provider_usina_id",
      "providerId",
    ]);
    Object.assign(newErrors, requiredErrors);

    // Validar capacidade (obrigatória e deve ser positiva)
    const capacityError = validateCapacity(formData.capacity);
    if (capacityError) newErrors.capacity = capacityError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await createUsina(formData);
      navigate(`/admin/campus/detail/${campusId}`);
    } catch (error) {
      console.error("Erro ao criar usina:", error);
      alert("Erro ao criar usina. Tente novamente.");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    field: keyof CreateUsinaData,
    value: string | number
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleNumberChange = (field: keyof CreateUsinaData, value: string) => {
    const numericValue = parseNumber(value);
    handleInputChange(field, numericValue);
  };

  const handleIntegerChange = (field: keyof CreateUsinaData, value: string) => {
    const integerValue = parseInteger(value);
    handleInputChange(field, integerValue);
  };

  if (!currentCampus) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Carregando campus...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to={`/admin/campus/detail/${campusId}`}>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Nova Usina</h1>
          <p className="text-gray-600">
            Adicionar usina ao campus {currentCampus.name}
          </p>
        </div>
      </div>

      <WidgetCard>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome da Usina *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Ex: Usina Solar 1"
                className="w-full"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Provedor *
              </label>
              <ProviderSelect
                value={formData.providerId}
                onChange={(value) => handleInputChange("providerId", value)}
                className="w-full"
              />
              {errors.providerId && (
                <p className="text-red-500 text-sm mt-1">{errors.providerId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Painel do Provedor
              </label>
              <select
                className="w-full border rounded h-9 px-3 text-sm"
                value={formData.provider_access || "default"}
                onChange={(e) => handleInputChange("provider_access", e.target.value)}
                disabled={!formData.providerId}
              >
                <option value="default">default</option>
                {accessList
                  ?.filter((a) => a.provider_access !== "default")
                  .map((a: ProviderDataAccess) => (
                    <option key={a.id} value={a.provider_access}>
                      {a.provider_access}
                    </option>
                  ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Capacidade (kW) *
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.capacity}
                onChange={(e) => handleNumberChange("capacity", e.target.value)}
                placeholder="Ex: 100.5"
                className="w-full"
              />
              {errors.capacity && (
                <p className="text-red-500 text-sm mt-1">{errors.capacity}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Modelo
              </label>
              <Input
                type="text"
                value={formData.model}
                onChange={(e) => handleInputChange("model", e.target.value)}
                placeholder="Ex: Canadian Solar 450W"
                className="w-full"
              />
              {errors.model && (
                <p className="text-red-500 text-sm mt-1">{errors.model}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantidade de Módulos
              </label>
              <Input
                type="number"
                value={formData.module_qtd}
                onChange={(e) =>
                  handleIntegerChange("module_qtd", e.target.value)
                }
                placeholder="Ex: 200"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Eficiência (%)
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.eficiency}
                onChange={(e) =>
                  handleNumberChange("eficiency", e.target.value)
                }
                placeholder="Ex: 85.5"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Área (m²)
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.area_m2}
                onChange={(e) => handleNumberChange("area_m2", e.target.value)}
                placeholder="Ex: 1500.75"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Azimute (°)
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.azimut}
                onChange={(e) => handleNumberChange("azimut", e.target.value)}
                placeholder="Ex: 180"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ID Interno do Provedor *
              </label>
              <Input
                type="text"
                value={formData.provider_usina_id}
                onChange={(e) =>
                  handleInputChange("provider_usina_id", e.target.value)
                }
                placeholder="Ex: USN001"
                className="w-full"
              />
              {errors.provider_usina_id && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.provider_usina_id}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitude
              </label>
              <Input
                type="number"
                step="any"
                value={formData.lat}
                onChange={(e) => handleNumberChange("lat", e.target.value)}
                placeholder="Ex: -29.1684486"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitude
              </label>
              <Input
                type="number"
                step="any"
                value={formData.long}
                onChange={(e) => handleNumberChange("long", e.target.value)}
                placeholder="Ex: -51.5132025"
                className="w-full"
              />
            </div>
          </div>

          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex items-center gap-3">
              <label className="text-sm font-medium text-gray-700">
                Status da Usina
              </label>
              <div className="flex items-center gap-2">
                <span
                  className={`text-sm ${formData.status === "INACTIVE" ? "text-gray-900" : "text-gray-500"}`}
                >
                  Inativo
                </span>
                <Switch
                  checked={formData.status === "ACTIVE"}
                  onCheckedChange={(checked) =>
                    handleInputChange("status", checked ? "ACTIVE" : "INACTIVE")
                  }
                />
                <span
                  className={`text-sm ${formData.status === "ACTIVE" ? "text-gray-900" : "text-gray-500"}`}
                >
                  Ativo
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 pt-4">
            <Button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              {loading ? "Salvando..." : "Salvar Usina"}
            </Button>
            <Link to={`/admin/campus/detail/${campusId}`}>
              <Button variant="outline" disabled={loading}>
                Cancelar
              </Button>
            </Link>
          </div>
        </form>
      </WidgetCard>
    </div>
  );
}

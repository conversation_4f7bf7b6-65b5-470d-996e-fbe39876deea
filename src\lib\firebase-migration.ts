import {
  collection,
  doc,
  getDocs,
  orderBy,
  query,
  writeBatch
} from "firebase/firestore";
import { db } from "./firebase";

// Tipos para migração
interface MigrationData {
  campus: any[];
  providers: any[];
  usinas: any[];
  providersDataAccess: any[];
  metadata: {
    exportedAt: string;
    totalRecords: number;
    version: string;
  };
}

// Função para exportar dados do emulador
export async function exportFirebaseData(): Promise<MigrationData> {
  try {
    console.log("📤 Iniciando exportação de dados...");

    // Exportar campus
    const campusSnapshot = await getDocs(query(collection(db, "campus"), orderBy("name")));
    const campusData = campusSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Exportar providers
    const providersSnapshot = await getDocs(query(collection(db, "providers"), orderBy("name")));
    const providersData = providersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Exportar usinas
    const usinasSnapshot = await getDocs(query(collection(db, "usinas"), orderBy("name")));
    const usinasData = usinasSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Exportar providersDataAccess
    const accessSnapshot = await getDocs(query(collection(db, "providersDataAccess"), orderBy("provider_id")));
    const providersDataAccess = accessSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    const migrationData: MigrationData = {
      campus: campusData,
      providers: providersData,
      usinas: usinasData,
      providersDataAccess,
      metadata: {
        exportedAt: new Date().toISOString(),
        totalRecords:
          campusData.length + providersData.length + usinasData.length + providersDataAccess.length,
        version: "1.0.0"
      }
    };

    console.log(`✅ Exportação concluída: ${migrationData.metadata.totalRecords} registros`);
    console.log(`📊 Campus: ${campusData.length}`);
    console.log(`📊 Providers: ${providersData.length}`);
    console.log(`📊 Usinas: ${usinasData.length}`);
    console.log(`🔐 ProvidersDataAccess: ${providersDataAccess.length}`);

    return migrationData;
  } catch (error) {
    console.error("❌ Erro na exportação:", error);
    throw error;
  }
}

// Função para gerar arquivo JSON para download
export function downloadMigrationFile(data: MigrationData, filename: string = "firebase-migration.json") {
  const jsonString = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonString], { type: "application/json" });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
  console.log("💾 Arquivo de migração baixado:", filename);
}

// Função para importar dados em produção (usar com configuração de produção)
export async function importFirebaseData(
  productionDb: any,
  data: MigrationData,
  options: {
    preserveIds?: boolean;
    skipExisting?: boolean;
    dryRun?: boolean;
  } = {}
) {
  const { preserveIds = false, skipExisting = false, dryRun = false } = options;

  try {
    console.log("📥 Iniciando importação de dados...");
    if (dryRun) {
      console.log("🧪 MODO DRY RUN - Nenhum dado será alterado");
    }

    const batch = writeBatch(productionDb);
    let importedCount = 0;
    let skippedCount = 0;

    // Importar providers primeiro (dependências)
    console.log("📝 Importando providers...");
    for (const provider of data.providers) {
      try {
        if (dryRun) {
          console.log(`  [DRY RUN] Provider: ${provider.name}`);
          continue;
        }

        const providerRef = preserveIds
          ? doc(productionDb, "providers", provider.id)
          : doc(collection(productionDb, "providers"));

        const providerData = { ...provider };
        if (!preserveIds) {
          delete providerData.id;
        }

        batch.set(providerRef, providerData);
        importedCount++;
        console.log(`✅ Provider: ${provider.name}`);
      } catch (error) {
        console.error(`❌ Erro ao importar provider ${provider.name}:`, error);
      }
    }

    // Importar tabela de acessos de painel (providersDataAccess)
    console.log("📝 Importando providersDataAccess...");
    for (const access of data.providersDataAccess || []) {
      try {
        if (dryRun) {
          console.log(`  [DRY RUN] Access: ${access.provider_access} (provider: ${access.provider_id})`);
          continue;
        }

        const accessRef = preserveIds
          ? doc(productionDb, "providersDataAccess", access.id)
          : doc(collection(productionDb, "providersDataAccess"));

        const accessData = { ...access };
        if (!preserveIds) delete accessData.id;

        batch.set(accessRef, accessData);
        importedCount++;
      } catch (error) {
        console.error(`❌ Erro ao importar access ${access.provider_access}:`, error);
      }
    }

    // Importar campus
    console.log("📝 Importando campus...");
    for (const campus of data.campus) {
      try {
        if (dryRun) {
          console.log(`  [DRY RUN] Campus: ${campus.name}`);
          continue;
        }

        const campusRef = preserveIds
          ? doc(productionDb, "campus", campus.id)
          : doc(collection(productionDb, "campus"));

        const campusData = { ...campus };
        if (!preserveIds) {
          delete campusData.id;
        }

        batch.set(campusRef, campusData);
        importedCount++;
        console.log(`✅ Campus: ${campus.name}`);
      } catch (error) {
        console.error(`❌ Erro ao importar campus ${campus.name}:`, error);
      }
    }

    // Importar usinas (dependem de campus e providers)
    console.log("📝 Importando usinas...");
    for (const usina of data.usinas) {
      try {
        if (dryRun) {
          console.log(`  [DRY RUN] Usina: ${usina.name}`);
          continue;
        }

        const usinaRef = preserveIds
          ? doc(productionDb, "usinas", usina.id)
          : doc(collection(productionDb, "usinas"));

        const usinaData = { ...usina };
        if (!preserveIds) {
          delete usinaData.id;
          // Nota: campusId e providerId precisarão ser mapeados para novos IDs
          console.log(`⚠️  Usina ${usina.name} - IDs de referência precisarão ser atualizados`);
        }

        batch.set(usinaRef, usinaData);
        importedCount++;
        console.log(`✅ Usina: ${usina.name}`);
      } catch (error) {
        console.error(`❌ Erro ao importar usina ${usina.name}:`, error);
      }
    }

    if (!dryRun) {
      console.log("🚀 Commitando batch...");
      await batch.commit();
      console.log("✅ Batch commitado com sucesso!");
    }

    console.log(`🎉 Importação concluída: ${importedCount} registros importados`);
    if (skippedCount > 0) {
      console.log(`⏭️  ${skippedCount} registros pulados`);
    }

    return { success: true, importedCount, skippedCount };
  } catch (error) {
    console.error("❌ Erro na importação:", error);
    throw error;
  }
}

// Função para gerar script SQL (opcional, para outros bancos)
export function generateSQLScript(data: MigrationData): string {
  let sql = `-- Script de migração Firebase para SQL\n`;
  sql += `-- Gerado em: ${data.metadata.exportedAt}\n`;
  sql += `-- Total de registros: ${data.metadata.totalRecords}\n\n`;

  // Campus
  sql += `-- Campus\n`;
  data.campus.forEach(campus => {
    sql += `INSERT INTO campus (name, city, lat, long, status, created_at, updated_at) VALUES `;
    sql += `('${campus.name}', '${campus.city}', ${campus.lat}, ${campus.long}, '${campus.status}', `;
    sql += `'${campus.createdAt}', '${campus.updatedAt}');\n`;
  });

  // Providers
  sql += `\n-- Providers\n`;
  data.providers.forEach(provider => {
    sql += `INSERT INTO providers (name, api_url, api_key, status, created_at, updated_at) VALUES `;
    sql += `('${provider.name}', '${provider.api_url}', '${provider.api_key}', '${provider.status}', `;
    sql += `'${provider.createdAt}', '${provider.updatedAt}');\n`;
  });

  // Usinas
  sql += `\n-- Usinas\n`;
  data.usinas.forEach(usina => {
    sql += `INSERT INTO usinas (name, capacity, lat, long, model, module_qtd, eficiency, `;
    sql += `area_m2, azimut, provider_usina_id, campus_id, provider_id, status, created_at, updated_at) VALUES `;
    sql += `('${usina.name}', ${usina.capacity}, ${usina.lat}, ${usina.long}, '${usina.model}', `;
    sql += `${usina.module_qtd}, ${usina.eficiency}, ${usina.area_m2}, ${usina.azimut}, `;
    sql += `'${usina.provider_usina_id}', '${usina.campusId}', '${usina.providerId}', '${usina.status}', `;
    sql += `'${usina.createdAt}', '${usina.updatedAt}');\n`;
  });

  return sql;
}

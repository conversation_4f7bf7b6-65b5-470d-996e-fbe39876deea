// Tipo base independente (sem Amplify)
export interface Provider {
  id: string;
  name: string;
  api_url: string;
  api_token: string;
  status: "ACTIVE" | "INACTIVE";
  createdAt: string;
  updatedAt: string;
}

// Tipos para formulários
export interface CreateProviderData {
  name: string;
  api_url: string;
  status: "ACTIVE" | "INACTIVE";
}

export interface UpdateProviderData {
  name: string;
  api_url: string;
  status: "ACTIVE" | "INACTIVE";
}

// Tipos para validação
export interface ProviderFormErrors {
  name?: string;
  api_url?: string;
  status?: string;
}

// Tipo para listagem com informações adicionais
export interface ProviderListItem extends Provider {
  usinasCount?: number; // Número de usinas vinculadas (para futuro uso)
}

// Union types específicos para campos de formulário de Provider
export type ProviderFormValue = string;

// Nova tabela: acessos a painéis de providers
export interface ProviderDataAccess {
  id: string;
  provider_id: string;
  provider_slug: string;
  provider_access: string; // ex: default, dashboard, api-secundaria
  panel_url: string;
  panel_user: string;
  panel_password: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProviderDataAccess {
  provider_id: string;
  provider_slug: string;
  provider_access: string;
  panel_url: string;
  panel_user: string;
  panel_password: string;
}

export interface UpdateProviderDataAccess extends CreateProviderDataAccess { }

import { db } from '@/lib/firebase';
import { collection, getDocs } from 'firebase/firestore';
import { useEffect, useState } from 'react';

interface DataDebugInfo {
  providers: Record<string, unknown>[];
  campus: Record<string, unknown>[];
  usinas: Record<string, unknown>[];
  dataAccess: Record<string, unknown>[];
}

interface ModuleItem {
  name: string;
  id: string;
  related?: number;
  usinas?: string[];
  campusId?: any;
  providerId?: any;
}

// Hook para gerenciar dados de debug
export const useDataDebugInfo = () => {
  const [data, setData] = useState<DataDebugInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const fetchData = async () => {
    setLoading(true);
    setErrors([]);

    try {
      const [providersSnapshot, campusSnapshot, usinasSnapshot, dataAccessSnapshot] = await Promise.all([
        getDocs(collection(db, 'providers')),
        getDocs(collection(db, 'campus')),
        getDocs(collection(db, 'usinas')),
        getDocs(collection(db, 'providersDataAccess'))
      ]);

      const providers = providersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const campus = campusSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const usinas = usinasSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const dataAccess = dataAccessSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      setData({ providers, campus, usinas, dataAccess });

      // Verificar problemas
      const newErrors: string[] = [];

      // Verificar usinas sem campus
      const usinasSemCampus = usinas.filter(u => !(u as any).campusId);
      if (usinasSemCampus.length > 0) {
        newErrors.push(`${usinasSemCampus.length} usinas sem campus relacionado`);
      }

      // Verificar usinas sem provider
      const usinasSemProvider = usinas.filter(u => !(u as any).providerId);
      if (usinasSemProvider.length > 0) {
        newErrors.push(`${usinasSemProvider.length} usinas sem provider relacionado`);
      }

      // Verificar campus sem usinas
      const campusSemUsinas = campus.filter(c =>
        !usinas.some(u => (u as any).campusId === c.id)
      );
      if (campusSemUsinas.length > 0) {
        newErrors.push(`${campusSemUsinas.length} campus sem usinas relacionadas`);
      }

      setErrors(newErrors);

    } catch (error) {
      console.error('Erro ao buscar dados:', error);
      setErrors([`Erro ao buscar dados: ${error}`]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return { data, loading, errors, fetchData };
};

// Componente para mostrar dados resumidos de um módulo específico
export const ModuleDataSummary = ({
  moduleType,
  data,
  errors
}: {
  moduleType: 'campus' | 'providers' | 'usinas';
  data: DataDebugInfo | null;
  errors: string[];
}) => {
  if (!data) return null;

  const getModuleData = () => {
    switch (moduleType) {
      case 'campus':
        return {
          count: data.campus.length,
          icon: '🏫',
          title: 'Campus',
          items: data.campus.map(c => ({
            name: c.name as string,
            id: c.id as string,
            related: data.usinas.filter(u => (u as any).campusId === c.id).length,
            usinas: data.usinas.filter(u => (u as any).campusId === c.id).map(u => u.name as string)
          } as ModuleItem))
        };
      case 'providers':
        return {
          count: data.providers.length,
          icon: '🔌',
          title: 'Fabricantes',
          items: data.providers.map(p => ({
            name: p.name as string,
            id: p.id as string,
            related: data.usinas.filter(u => (u as any).providerId === p.id).length,
            usinas: data.usinas.filter(u => (u as any).providerId === p.id).map(u => u.name as string)
          } as ModuleItem))
        };
      case 'usinas':
        return {
          count: data.usinas.length,
          icon: '📊',
          title: 'Usinas',
          items: data.usinas.map(u => ({
            name: u.name as string,
            id: u.id as string,
            campusId: (u as any).campusId,
            providerId: (u as any).providerId
          } as ModuleItem))
        };
    }
  };


  const moduleData = getModuleData();
  if (!moduleData) return null;

  return (
    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center gap-2 mb-2">
        <span className="text-lg">{moduleData.icon}</span>
        <span className="text-sm font-medium text-gray-700">
          {moduleData.title}: {moduleData.count}
        </span>
      </div>

      {/* Lista de itens */}
      <div className="space-y-1">
        {moduleData.items.map((item) => (
          <div key={item.id} className="text-sm border-l-4 pl-3 py-1 text-gray-600 flex flex-col">
            <div className="flex justify-between">
              <span className="truncate">{item.name}</span>
              {moduleType === 'campus' && item.related !== undefined && (
                <span className={`text-xs ${item.related > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {item.related > 0 ? `Usinas: ${item.related}` : 'Nenhuma usina cadastrada'}
                </span>
              )}
              {moduleType === 'providers' && item.related !== undefined && (
                <span className={`text-xs ${item.related > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {item.related > 0 ? `Usinas: ${item.related}` : 'Nenhuma usina cadastrada'}
                </span>
              )}
            </div>
            {moduleType === 'campus' && item.related !== undefined && item.related > 0 && item.usinas && (
              <span className={`text-xs text-green-700`}>
                {item.usinas.join(', ')}
              </span>
            )}
          </div>
        ))}
      </div>

      {/* Mostrar erros relacionados */}
      {errors.length > 0 && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
          <div className="text-red-700 font-medium">⚠️ Problemas:</div>
          {errors.filter(error =>
            error.toLowerCase().includes(moduleType === 'usinas' ? 'usina' : moduleType.slice(0, -1))
          ).slice(0, 2).map((error, index) => (
            <div key={index} className="text-red-600">• {error}</div>
          ))}
        </div>
      )}
    </div>
  );
};

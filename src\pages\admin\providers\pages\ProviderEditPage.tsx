import WidgetCard from "@/components/shared/widget-card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type {
  CreateProviderDataAccess,
  Provider,
  ProviderDataAccess,
  ProviderFormErrors,
  UpdateProviderData,
  UpdateProviderDataAccess,
} from "@/types";
import { ArrowLeft, Check } from "lucide-react";
import { FormEvent, useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useProviderAccess } from "../hooks/useProviderAccess";
import { useProviders } from "../hooks/useProviders";

export default function ProviderEditPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const {
    providers,
    updateProvider,
    loading: providersLoading,
  } = useProviders();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<UpdateProviderData>({
    name: "",
    api_url: "",
    status: "ACTIVE",
  });
  const [errors, setErrors] = useState<ProviderFormErrors>({});
  const [provider, setProvider] = useState<Provider | null>(null);
  const { accessList, createAccess, updateAccess, deleteAccess } =
    useProviderAccess(id);

  // Estados para controlar alterações nos acessos
  const [changedAccess, setChangedAccess] = useState<
    Record<string, ProviderDataAccess>
  >({});
  const [savingAccess, setSavingAccess] = useState<string | null>(null);

  const [newAccess, setNewAccess] = useState<CreateProviderDataAccess>({
    provider_id: id || "",
    provider_access: "default",
    panel_url: "",
    panel_user: "",
    panel_password: "",
  });

  useEffect(() => {
    if (providers && id) {
      const foundProvider = providers.find((p) => p.id === id);
      if (foundProvider) {
        setProvider(foundProvider);
        setFormData({
          name: foundProvider.name,
          api_url: foundProvider.api_url,
          status: foundProvider.status,
        });
      }
    }
  }, [providers, id]);

  const validateForm = (): boolean => {
    const newErrors: ProviderFormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome é obrigatório";
    }

    if (!formData.api_url.trim()) {
      newErrors.api_url = "URL é obrigatória";
    } else {
      // Validação básica de URL
      try {
        new URL(formData.api_url);
      } catch {
        newErrors.api_url = "URL inválida";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !id) {
      return;
    }

    setLoading(true);
    try {
      await updateProvider(id, formData);
      navigate("/admin/providers");
    } catch (error) {
      console.error("Erro ao atualizar provedor:", error);
      alert("Erro ao atualizar provedor. Tente novamente.");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    field: keyof UpdateProviderData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  // Lidar com alterações nos acessos
  const handleAccessChange = (
    accessId: string,
    field: keyof ProviderDataAccess,
    value: string
  ) => {
    const originalAccess = accessList?.find((a) => a.id === accessId);
    if (!originalAccess) return;

    const updatedAccess = {
      ...originalAccess,
      [field]: value,
    };

    setChangedAccess((prev) => ({
      ...prev,
      [accessId]: updatedAccess,
    }));
  };

  // Salvar alterações de um acesso específico
  const handleSaveAccess = async (accessId: string) => {
    const changedData = changedAccess[accessId];
    if (!changedData) return;

    setSavingAccess(accessId);
    try {
      await updateAccess(accessId, changedData as UpdateProviderDataAccess);
      // Remove das alterações após salvar
      setChangedAccess((prev) => {
        const { [accessId]: removed, ...rest } = prev;
        return rest;
      });
    } catch (error) {
      console.error("Erro ao salvar acesso:", error);
      alert("Erro ao salvar acesso. Tente novamente.");
    } finally {
      setSavingAccess(null);
    }
  };

  // Verificar se um acesso tem alterações
  const hasAccessChanges = (accessId: string): boolean => {
    return !!changedAccess[accessId];
  };

  if (providersLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Carregando provedor...</div>
      </div>
    );
  }

  if (!provider) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Provedor não encontrado</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/admin/providers">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            Editar Provedor
          </h1>
          <p className="text-gray-600">
            Edite as informações do provedor {provider.name}
          </p>
        </div>
      </div>

      <WidgetCard>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Ex: WEG Solar"
                className="w-full"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API URL *
              </label>
              <Input
                type="url"
                value={formData.api_url}
                onChange={(e) => handleInputChange("api_url", e.target.value)}
                placeholder="https://exemplo.com/api"
                className="w-full"
              />
              {errors.api_url && (
                <p className="mt-1 text-sm text-red-600">{errors.api_url}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <div className="flex items-center gap-2">
                <span
                  className={`text-sm ${formData.status === "INACTIVE" ? "text-gray-900" : "text-gray-500"}`}
                >
                  Inativo
                </span>
                <Input
                  type="checkbox"
                  checked={formData.status === "ACTIVE"}
                  onChange={(e) =>
                    handleInputChange(
                      "status",
                      e.target.checked ? "ACTIVE" : "INACTIVE"
                    )
                  }
                  className="h-4 w-4"
                />
                <span
                  className={`text-sm ${formData.status === "ACTIVE" ? "text-gray-900" : "text-gray-500"}`}
                >
                  Ativo
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 pt-6 border-t border-gray-200">
            <Button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              {loading ? "Salvando..." : "Salvar Alterações"}
            </Button>
            <Link to="/admin/providers">
              <Button variant="outline" disabled={loading}>
                Cancelar
              </Button>
            </Link>
          </div>
        </form>
      </WidgetCard>

      {/* Acessos de Painel do Provedor */}
      <WidgetCard title="Acessos de Painel">
        <div className="space-y-4">
          {/* Lista existente */}
          {accessList && accessList.length > 0 ? (
            <div className="border rounded-md divide-y">
              {accessList.map((acc: ProviderDataAccess) => {
                const currentData = changedAccess[acc.id] || acc;
                const hasChanges = hasAccessChanges(acc.id);
                const isSaving = savingAccess === acc.id;

                return (
                  <div
                    key={acc.id}
                    className="p-3 flex flex-col md:flex-row md:items-center gap-3"
                  >
                    <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-3">
                      <div>
                        <label className="text-xs text-gray-600">
                          Panel Name
                        </label>
                        <Input
                          value={currentData.provider_access}
                          onChange={(e) =>
                            handleAccessChange(
                              acc.id,
                              "provider_access",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div>
                        <label className="text-xs text-gray-600">
                          Panel URL
                        </label>
                        <Input
                          value={currentData.panel_url}
                          onChange={(e) =>
                            handleAccessChange(
                              acc.id,
                              "panel_url",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div>
                        <label className="text-xs text-gray-600">Usuário</label>
                        <Input
                          value={currentData.panel_user}
                          onChange={(e) =>
                            handleAccessChange(
                              acc.id,
                              "panel_user",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div>
                        <label className="text-xs text-gray-600">Senha</label>
                        <Input
                          type="password"
                          value={currentData.panel_password}
                          onChange={(e) =>
                            handleAccessChange(
                              acc.id,
                              "panel_password",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                    <div className="flex self-end gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleSaveAccess(acc.id)}
                        disabled={!hasChanges || isSaving}
                        className={
                          hasChanges
                            ? "bg-green-600 hover:bg-green-700 text-white"
                            : "bg-gray-400 text-white cursor-not-allowed"
                        }
                      >
                        {isSaving ? "Salvando..." : "Salvar"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteAccess(acc.id)}
                        disabled={isSaving}
                      >
                        Remover
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-sm text-gray-600">
              Nenhum acesso cadastrado.
            </div>
          )}

          {/* Adicionar novo */}
          <div className="pt-2 border-t">
            <h4 className="text-sm font-medium text-gray-800 mb-2">
              Adicionar Acesso
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
              <div>
                <label className="text-xs text-gray-600">Panel Name</label>
                <Input
                  value={newAccess.provider_access}
                  onChange={(e) =>
                    setNewAccess((p) => ({
                      ...p,
                      provider_access: e.target.value,
                    }))
                  }
                />
              </div>
              <div>
                <label className="text-xs text-gray-600">Panel URL</label>
                <Input
                  value={newAccess.panel_url}
                  onChange={(e) =>
                    setNewAccess((p) => ({ ...p, panel_url: e.target.value }))
                  }
                />
              </div>
              <div>
                <label className="text-xs text-gray-600">Usuário</label>
                <Input
                  value={newAccess.panel_user}
                  onChange={(e) =>
                    setNewAccess((p) => ({ ...p, panel_user: e.target.value }))
                  }
                />
              </div>
              <div>
                <label className="text-xs text-gray-600">Senha</label>
                <Input
                  type="password"
                  value={newAccess.panel_password}
                  onChange={(e) =>
                    setNewAccess((p) => ({
                      ...p,
                      panel_password: e.target.value,
                    }))
                  }
                />
              </div>
              <div className="flex items-end">
                <Button
                  className="w-full"
                  onClick={async () => {
                    if (!id) return;
                    await createAccess({ ...newAccess, provider_id: id });
                    setNewAccess({
                      provider_id: id,
                      provider_access: "default",
                      panel_url: "",
                      panel_user: "",
                      panel_password: "",
                    });
                  }}
                >
                  Adicionar
                </Button>
              </div>
            </div>
          </div>
        </div>
      </WidgetCard>
    </div>
  );
}

# <PERSON><PERSON><PERSON>lo Administrativo

Este módulo implementa as interfaces administrativas para gerenciar provedores, campus e usinas do sistema MonitoraLux.

## Estrutura

```
src/pages/admin/
├── index.tsx                    # Router principal do módulo admin
├── providers/                   # Módulo de Provedores
│   ├── index.tsx               # Router de provedores
│   ├── hooks/
│   │   └── useProviders.ts     # Hook para operações CRUD
│   └── pages/
│       ├── ProvidersListPage.tsx
│       ├── ProviderCreatePage.tsx
│       └── ProviderEditPage.tsx
├── campus/                      # Módulo de Campus
│   ├── index.tsx               # Router de campus
│   ├── hooks/
│   │   ├── useCampus.ts        # Hook para operações CRUD de campus
│   │   └── useUsinas.ts        # Hook para operações CRUD de usinas
│   └── pages/
│       ├── CampusListPage.tsx
│       ├── CampusCreatePage.tsx
│       ├── CampusEditPage.tsx
│       ├── CampusDetailPage.tsx
│       └── UsinaCreatePage.tsx
└── README.md                    # Esta documentação
```

## Rotas Administrativas

### Acesso Principal

- `/admin` - Dashboard administrativo

### Provedores

- `/admin/providers` - Lista de provedores
- `/admin/providers/create` - Criar novo provedor
- `/admin/providers/edit/:id` - Editar provedor

### Campus

- `/admin/campus` - Lista de campus
- `/admin/campus/create` - Criar novo campus
- `/admin/campus/edit/:id` - Editar campus
- `/admin/campus/detail/:id` - Detalhes do campus e suas usinas
- `/admin/campus/:campusId/usina/create` - Criar usina para um campus

## Funcionalidades

### 🏢 Gestão de Provedores

- ✅ Listar provedores com paginação
- ✅ Criar novos provedores
- ✅ Editar provedores existentes
- ✅ Excluir provedores
- ✅ Validação de URL e campos obrigatórios

### 🏫 Gestão de Campus

- ✅ Listar campus com informações de localização
- ✅ Criar novos campus
- ✅ Editar campus existentes
- ✅ Excluir campus
- ✅ Validação de coordenadas geográficas
- ✅ Status do campus (Ativo, Inativo, Manutenção)

### ⚡ Gestão de Usinas

- ✅ Visualizar usinas por campus
- ✅ Criar usinas vinculadas a campus e provedores
- ✅ Excluir usinas
- ✅ Validação completa de dados técnicos
- ✅ Relacionamento obrigatório com provedor

## Relacionamentos

### Campus → Usinas (1:N)

- Cada campus pode ter várias usinas
- Usinas são exibidas na página de detalhes do campus
- Exclusão de campus remove todas as usinas vinculadas

### Provider → Usinas (1:N)

- Cada usina deve ter um provedor obrigatório
- Provedores fornecem dados das usinas
- Seleção via dropdown nas interfaces

### Usina → Campus + Provider (N:1 cada)

- Usina pertence a um campus
- Usina tem um provedor de dados
- ID interno do provedor para consultas

## Componentes Compartilhados

### ProviderSelect

Componente para seleção de provedores:

```tsx
<ProviderSelect
  value={providerId}
  onChange={setProviderId}
  error={errors.providerId}
/>
```

### CampusSelect

Componente para seleção de campus:

```tsx
<CampusSelect value={campusId} onChange={setCampusId} error={errors.campusId} />
```

## Utilitários

### Validação

- Coordenadas geográficas (lat/lng)
- URLs válidas
- Capacidade e eficiência
- Azimute (0-360°)
- Área e quantidade de módulos

### Formatação

- Coordenadas com precisão
- Capacidade (kW/MW)
- Eficiência (%)
- Status traduzidos
- Datas em português

## Integração com Amplify

### Operações CRUD

- Criação, leitura, atualização e exclusão
- Sincronização em tempo real
- Validação de schema
- Relacionamentos automáticos

### Observadores

- Atualizações em tempo real
- Sincronização entre abas
- Notificação de mudanças

## Segurança

### Acesso Restrito

- Rotas administrativas separadas
- Futuro: autenticação e autorização
- Confirmação para exclusões

### Validação

- Validação client-side e server-side
- Sanitização de dados
- Prevenção de dados inválidos

## Próximos Passos

1. **Autenticação**: Implementar controle de acesso
2. **Permissões**: Diferentes níveis de usuário
3. **Auditoria**: Log de mudanças
4. **Bulk Operations**: Operações em lote
5. **Importação**: Upload de dados via CSV/Excel
6. **Relatórios**: Dashboards administrativos

## Como Usar

1. **Acesse o painel**: `http://localhost:3001/admin`
2. **Crie provedores**: Necessários antes de criar usinas
3. **Crie campus**: Localizações das usinas
4. **Adicione usinas**: Vincule a campus e provedores
5. **Gerencie dados**: Use as interfaces CRUD conforme necessário

## Dependências

- React Router para navegação
- Shadcn/UI para componentes
- TanStack Table para tabelas
- Amplify Gen 2 para backend
- Utilitários de validação e formatação

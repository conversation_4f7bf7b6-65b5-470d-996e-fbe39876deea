import { CustomTooltip } from "@/components/shared/custom-tooltip";
import WidgetCard from "@/components/shared/widget-card";
import { GraphPoint } from "@/types";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function StackedAreaChart({
  title,
  className,
  data,
}: {
  title: string;
  className?: string;
  data: GraphPoint[];
}) {
  // Valor máximo será o maior valor da lista + 20% e arredondado para cima
  const maxGeneration = Math.max(
    ...data.map((point) =>
      Math.ceil((point.generation || 0) + (point.generation || 0) * 0.1)
    )
  );
  const maxIrradiation =
    Math.max(
      ...data.map((point) =>
        Math.ceil((point.irradiation || 0) + (point.irradiation || 0) * 0.1)
      )
    ) || 700;

  return (
    <WidgetCard title={title} className={className}>
      <div className="mt-5 aspect-[1060/400] w-full lg:mt-7">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{
              top: 10,
              right: -10,
              left: -20,
              bottom: 0,
            }}
            className="[&_.recharts-cartesian-grid-vertical]:opacity-0 text-sm"
          >
            <defs>
              <linearGradient
                id="stackedAreaChart1"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop
                  offset="5%"
                  stopColor="#e63946"
                  className="[stop-opacity:0.5] dark:[stop-opacity:0.3]"
                />
                <stop offset="95%" stopColor="#e63946" stopOpacity={0} />
              </linearGradient>
              <linearGradient
                id="stackedAreaChart2"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop
                  offset="5%"
                  stopColor="orange"
                  className="[stop-opacity:0.5] dark:[stop-opacity:0.3]"
                />
                <stop offset="95%" stopColor="orange" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="time" />
            <YAxis
              yAxisId="left"
              domain={[0, maxGeneration]}
              label={{
                value: "Geração (kW)",
                angle: -90,
                position: "insideRight",
                offset: -20,
                style: { textAnchor: "middle", fontWeight: 500 },
              }}
              tick={{ dx: -5 }}
              stroke="#e63946"
              allowDataOverflow
            />
            <YAxis
              yAxisId="right"
              orientation="right"
              domain={[0, maxIrradiation]}
              label={{
                value: "Irradiação (W/m²)",
                angle: 90,
                position: "insideLeft",
                offset: -20,
                style: { textAnchor: "middle", fontWeight: 500 },
              }}
              tick={{ dx: 5 }}
              stroke="orange"
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="generation"
              yAxisId="left"
              strokeWidth={2}
              stroke="#e63946"
              fill="url(#stackedAreaChart1)"
              name="Geração"
              dot={false}
              activeDot={{ r: 5, fill: "#e63946" }}
            />
            <Area
              type="monotone"
              dataKey="irradiation"
              yAxisId="right"
              strokeWidth={2}
              stroke="orange"
              fill="url(#stackedAreaChart2)"
              name="Irradiação"
              dot={false}
              activeDot={{ r: 5, fill: "orange" }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </WidgetCard>
  );
}

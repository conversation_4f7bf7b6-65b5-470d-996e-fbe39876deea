import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
} from "@tanstack/react-table";
import { useState } from "react";

interface UseTanStackTableProps<TData> {
  tableData: TData[];
  columnConfig: ColumnDef<TData>[];
  options?: {
    initialState?: {
      pagination?: {
        pageIndex: number;
        pageSize: number;
      };
      sorting?: SortingState;
      columnFilters?: ColumnFiltersState;
      columnVisibility?: VisibilityState;
    };
    meta?: {
      handleDeleteRow?: (row: TData) => void;
    };
    enableColumnResizing?: boolean;
    enableSorting?: boolean;
    enableFiltering?: boolean;
  };
}

export const useTanStackTable = <TData>({
  tableData,
  columnConfig,
  options = {},
}: UseTanStackTableProps<TData>) => {
  const [data, setData] = useState<TData[]>(tableData);
  const [sorting, setSorting] = useState<SortingState>(
    options.initialState?.sorting ?? []
  );
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
    options.initialState?.columnFilters ?? []
  );
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    options.initialState?.columnVisibility ?? {}
  );

  const table = useReactTable({
    data,
    columns: columnConfig,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      ...options.initialState,
    },
    meta: options.meta,
    enableColumnResizing: options.enableColumnResizing ?? false,
    enableSorting: options.enableSorting ?? true,
    enableColumnFilters: options.enableFiltering ?? true,
  });

  return {
    table,
    setData,
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    columnVisibility,
    setColumnVisibility,
  };
};

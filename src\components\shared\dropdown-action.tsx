import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/utils/merge-classes";
import { Calendar } from "lucide-react";
import { useState } from "react";

type Options = {
  value: string;
  label: string;
};

type DropdownActionProps = {
  name?: string;
  options: Options[];
  defaultActive?: string;
  onChange: (data: string) => void;
  className?: string;
  dropdownClassName?: string;
  activeClassName?: string;
  prefixIconClassName?: string;
  suffixIconClassName?: string;
  selectClassName?: string;
};

export default function DropdownAction({
  options,
  onChange,
  className,
  prefixIconClassName,
  selectClassName,
}: DropdownActionProps) {
  const [viewType, setViewType] = useState(options[0]?.value || "");

  function handleOnChange(value: string) {
    setViewType(value);
    onChange(value);
  }

  return (
    <Select value={viewType} onValueChange={handleOnChange}>
      <SelectTrigger
        className={cn("w-auto py-1 px-2 h-8", selectClassName, className)}
      >
        <div className="flex items-center gap-2">
          <Calendar
            className={cn("h-5 w-5 text-gray-500", prefixIconClassName)}
          />
          <SelectValue />
        </div>
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

import WidgetCard from "@/components/shared/widget-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import type { CampusFormErrors, CreateCampusData } from "@/types";
import {
  parseNumber,
  validateCoordinates,
  validateRequiredFields,
} from "@/utils";
import { ArrowLeft, Check } from "lucide-react";
import { FormEvent, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useCampus } from "../hooks/useCampus";

export default function CampusCreatePage() {
  const navigate = useNavigate();
  const { createCampus } = useCampus();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateCampusData>({
    name: "",
    city: "",
    lat: 0,
    long: 0,
    status: "ACTIVE",
  });
  const [errors, setErrors] = useState<CampusFormErrors>({});

  const validateForm = (): boolean => {
    // Validar campos obrigatórios
    const requiredErrors = validateRequiredFields(formData, ["name", "city"]);

    // Validar coordenadas
    const coordinateErrors = validateCoordinates(formData.lat, formData.long);

    const allErrors = {
      ...requiredErrors,
      ...coordinateErrors,
    };

    setErrors(allErrors);
    return Object.keys(allErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await createCampus(formData);
      navigate("/admin/campus");
    } catch (error) {
      console.error("Erro ao criar campus:", error);
      alert("Erro ao criar campus. Tente novamente.");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    field: keyof CreateCampusData,
    value: string | number
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleCoordinateChange = (field: "lat" | "long", value: string) => {
    const numericValue = parseNumber(value);
    handleInputChange(field, numericValue);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/admin/campus">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Novo Campus</h1>
          <p className="text-gray-600">Adicione um novo campus ao sistema</p>
        </div>
      </div>

      <WidgetCard>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome do Campus *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Ex: Campus Feliz"
                className="w-full"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cidade *
              </label>
              <Input
                type="text"
                value={formData.city}
                onChange={(e) => handleInputChange("city", e.target.value)}
                placeholder="Ex: Feliz"
                className="w-full"
              />
              {errors.city && (
                <p className="mt-1 text-sm text-red-600">{errors.city}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitude *
              </label>
              <Input
                type="number"
                step="any"
                value={formData.lat}
                onChange={(e) => handleCoordinateChange("lat", e.target.value)}
                placeholder="Ex: -29.1684486"
                className="w-full"
              />
              {errors.lat && (
                <p className="mt-1 text-sm text-red-600">{errors.lat}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitude *
              </label>
              <Input
                type="number"
                step="any"
                value={formData.long}
                onChange={(e) => handleCoordinateChange("long", e.target.value)}
                placeholder="Ex: -51.5132025"
                className="w-full"
              />
              {errors.long && (
                <p className="mt-1 text-sm text-red-600">{errors.long}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Status do Campus
              </label>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-700">
                      Campus Ativo
                    </span>
                    <div className="flex items-center gap-2">
                      <span
                        className={`text-sm ${formData.status === "INACTIVE" ? "text-gray-900" : "text-gray-500"}`}
                      >
                        Inativo
                      </span>
                      <Switch
                        checked={formData.status === "ACTIVE"}
                        onCheckedChange={(checked) =>
                          handleInputChange(
                            "status",
                            checked ? "ACTIVE" : "INACTIVE"
                          )
                        }
                        disabled={formData.status === "MAINTENANCE"}
                      />
                      <span
                        className={`text-sm ${formData.status === "ACTIVE" ? "text-gray-900" : "text-gray-500"}`}
                      >
                        Ativo
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-700">
                      Modo Manutenção
                    </span>
                    <div className="flex items-center gap-2">
                      <span
                        className={`text-sm ${formData.status !== "MAINTENANCE" ? "text-gray-900" : "text-gray-500"}`}
                      >
                        Normal
                      </span>
                      <Switch
                        checked={formData.status === "MAINTENANCE"}
                        onCheckedChange={(checked) =>
                          handleInputChange(
                            "status",
                            checked ? "MAINTENANCE" : "ACTIVE"
                          )
                        }
                      />
                      <span
                        className={`text-sm ${formData.status === "MAINTENANCE" ? "text-gray-900" : "text-gray-500"}`}
                      >
                        Manutenção
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 pt-6 border-t border-gray-200">
            <Button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              {loading ? "Salvando..." : "Salvar Campus"}
            </Button>
            <Link to="/admin/campus">
              <Button variant="outline" disabled={loading}>
                Cancelar
              </Button>
            </Link>
          </div>
        </form>
      </WidgetCard>
    </div>
  );
}

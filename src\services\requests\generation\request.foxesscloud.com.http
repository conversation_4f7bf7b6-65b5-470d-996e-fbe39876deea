## Página inicial
## https://www.foxesscloud.com/bus/dataView

## Página de Campus (Restinga)
## Detalhes gerais

GET https://www.foxesscloud.com/generic/w/v0/plant/earnings/detail?stationID=f4495405-ee45-4d6c-9494-570f2289d3d2 HTTP/1.1
signature: 9603f154482e9e586dc68557fd76a5d8.5245784
token: eyJpZCI6IjQ1MDMwNWNkLWNiZWMtNGVmMC05ZWQ1LTQwNjg4N2IyZDE5MiIsInNlY3JldCI6IjM5ODU5NWExNzUwYTM3Y2RmNjJiM2I4ZGU0NmI2MWE5YWRjNTI3NWFjMmM5NmQyYzFiZDgzMjgwYmQ5ZmU2NmMiLCJwYXlsb2FkIjoiWDJRd04wWGRSM0tQV3NVNU5WSnhzM1NBMit2azRrOVUrVnl3bzlyZHpUTUNiTnlqTk5ic3VzNjFrTVUycU5uYkllVWJXcmRWTmYzaGRKSG1rempHOXRKVFdlSzM4ODBNMEhMVDVFL0U4YTFodXFBejU1TUN6cTBuT0pKTDVNNzd1eUI2OFN0OFFBa0EwUStLblJUaUQxaWgycm9jNkR2KzFTRGRGTGdEOW5LQjhYellTZTFCbm1Nb0lCTDNCNzVvVyt2Z1dVYXpMQmlMbXRpdHBUb2RrUXF3L0hkcDFaaFdpRWNyZW9CNmtOZTh4NHIreDc2SmdCamRMTHY2ZzYwciJ9
timestamp: 1745096701447
timezone: America/Sao_Paulo
lang: en

################################
## POSTs não acessíveis sem o signature atualizado

## Detalhes por inverter - sem coordenadas
POST https://www.foxesscloud.com/c/v0/plant/device/list HTTP/1.1
Content-Type: application/json
signature: 154abc5929b7812ccd4c8672e2682d1c.5245784
timestamp: 1745102332177
token: eyJpZCI6IjQ1MDMwNWNkLWNiZWMtNGVmMC05ZWQ1LTQwNjg4N2IyZDE5MiIsInNlY3JldCI6IjM5ODU5NWExNzUwYTM3Y2RmNjJiM2I4ZGU0NmI2MWE5YWRjNTI3NWFjMmM5NmQyYzFiZDgzMjgwYmQ5ZmU2NmMiLCJwYXlsb2FkIjoiWDJRd04wWGRSM0tQV3NVNU5WSnhzM1NBMit2azRrOVUrVnl3bzlyZHpUTUNiTnlqTk5ic3VzNjFrTVUycU5uYkllVWJXcmRWTmYzaGRKSG1rempHOXRKVFdlSzM4ODBNMEhMVDVFL0U4YTFodXFBejU1TUN6cTBuT0pKTDVNNzd1eUI2OFN0OFFBa0EwUStLblJUaUQxaWgycm9jNkR2KzFTRGRGTGdEOW5LQjhYellTZTFCbm1Nb0lCTDNCNzVvVyt2Z1dVYXpMQmlMbXRpdHBUb2RrUXF3L0hkcDFaaFdpRWNyZW9CNmtOZTh4NHIreDc2SmdCamRMTHY2ZzYwciJ9
timezone: America/Sao_Paulo
lang: en
stationID: f4495405-ee45-4d6c-9494-570f2289d3d2

{
  "pageSize": 10,
  "currentPage": 1,
  "total": 0,
  "condition": {
    "status": 0,
    "batteryMainSN": "",
    "deviceSN": "",
    "deviceType": "",
    "moduleSN": ""
  }
}

## Warnings - erros
POST https://www.foxesscloud.com/ticket/v0/list HTTP/1.1
Content-Type: application/json
signature: 32e6eac7f591db8895b01f50baaae6da.5245784
timestamp: 1745102139351
token: eyJpZCI6IjQ1MDMwNWNkLWNiZWMtNGVmMC05ZWQ1LTQwNjg4N2IyZDE5MiIsInNlY3JldCI6IjM5ODU5NWExNzUwYTM3Y2RmNjJiM2I4ZGU0NmI2MWE5YWRjNTI3NWFjMmM5NmQyYzFiZDgzMjgwYmQ5ZmU2NmMiLCJwYXlsb2FkIjoiWDJRd04wWGRSM0tQV3NVNU5WSnhzM1NBMit2azRrOVUrVnl3bzlyZHpUTUNiTnlqTk5ic3VzNjFrTVUycU5uYkllVWJXcmRWTmYzaGRKSG1rempHOXRKVFdlSzM4ODBNMEhMVDVFL0U4YTFodXFBejU1TUN6cTBuT0pKTDVNNzd1eUI2OFN0OFFBa0EwUStLblJUaUQxaWgycm9jNkR2KzFTRGRGTGdEOW5LQjhYellTZTFCbm1Nb0lCTDNCNzVvVyt2Z1dVYXpMQmlMbXRpdHBUb2RrUXF3L0hkcDFaaFdpRWNyZW9CNmtOZTh4NHIreDc2SmdCamRMTHY2ZzYwciJ9
timezone: America/Sao_Paulo
lang: en

{
  "pageSize": 10,
  "currentPage": 1,
  "total": 0,
  "condition": {
    "ticketSerialNumber": ""
  }
}

## Gráfico de energia
POST https://www.foxesscloud.com/generic/w/v0/plant/history/raw HTTP/1.1
Content-Type: application/json
signature: 9c941593a80f9ae60ca36cc199eadd42.5245784
timestamp: 1745096701146
token: eyJpZCI6IjQ1MDMwNWNkLWNiZWMtNGVmMC05ZWQ1LTQwNjg4N2IyZDE5MiIsInNlY3JldCI6IjM5ODU5NWExNzUwYTM3Y2RmNjJiM2I4ZGU0NmI2MWE5YWRjNTI3NWFjMmM5NmQyYzFiZDgzMjgwYmQ5ZmU2NmMiLCJwYXlsb2FkIjoiWDJRd04wWGRSM0tQV3NVNU5WSnhzM1NBMit2azRrOVUrVnl3bzlyZHpUTUNiTnlqTk5ic3VzNjFrTVUycU5uYkllVWJXcmRWTmYzaGRKSG1rempHOXRKVFdlSzM4ODBNMEhMVDVFL0U4YTFodXFBejU1TUN6cTBuT0pKTDVNNzd1eUI2OFN0OFFBa0EwUStLblJUaUQxaWgycm9jNkR2KzFTRGRGTGdEOW5LQjhYellTZTFCbm1Nb0lCTDNCNzVvVyt2Z1dVYXpMQmlMbXRpdHBUb2RrUXF3L0hkcDFaaFdpRWNyZW9CNmtOZTh4NHIreDc2SmdCamRMTHY2ZzYwciJ9
timezone: America/Sao_Paulo
lang: en

{
  "isSplit": null,
  "stationID": "f4495405-ee45-4d6c-9494-570f2289d3d2",
  "variables": ["generationPower", "feedinPower", "loadsPower", "gridConsumptionPower"],
  "timespan": "day",
  "beginDate": {
    "year": 2025,
    "month": 4,
    "day": 19,
    "hour": 0,
    "minute": 0,
    "second": 0
  }
}

## Historico diário - 1 mês
POST https://www.foxesscloud.com/generic/w/v0/plant/history/report HTTP/1.1
Content-Type: application/json
signature: 75b2f202d9d1d0937fe6d358223e62b8.5245784
timestamp: 1745106425558
token: eyJpZCI6IjQ1MDMwNWNkLWNiZWMtNGVmMC05ZWQ1LTQwNjg4N2IyZDE5MiIsInNlY3JldCI6IjM5ODU5NWExNzUwYTM3Y2RmNjJiM2I4ZGU0NmI2MWE5YWRjNTI3NWFjMmM5NmQyYzFiZDgzMjgwYmQ5ZmU2NmMiLCJwYXlsb2FkIjoiWDJRd04wWGRSM0tQV3NVNU5WSnhzM1NBMit2azRrOVUrVnl3bzlyZHpUTUNiTnlqTk5ic3VzNjFrTVUycU5uYkllVWJXcmRWTmYzaGRKSG1rempHOXRKVFdlSzM4ODBNMEhMVDVFL0U4YTFodXFBejU1TUN6cTBuT0pKTDVNNzd1eUI2OFN0OFFBa0EwUStLblJUaUQxaWgycm9jNkR2KzFTRGRGTGdEOW5LQjhYellTZTFCbm1Nb0lCTDNCNzVvVyt2Z1dVYXpMQmlMbXRpdHBUb2RrUXF3L0hkcDFaaFdpRWNyZW9CNmtOZTh4NHIreDc2SmdCamRMTHY2ZzYwciJ9
timezone: America/Sao_Paulo
lang: en

{
  "isSplit": null,
  "stationID": "f4495405-ee45-4d6c-9494-570f2289d3d2",
  "reportType": "day",
  "variables": ["feedin", "generation", "loads", "gridConsumption"],
  "queryDate": {
    "year": 2025,
    "month": 4,
    "day": 18,
    "hour": 21
  }
}
import { cn } from "@/utils/merge-classes";
import { TooltipProps } from "recharts";

interface CustomTooltipProps extends TooltipProps<number, string> {
  className?: string;
}

export function CustomTooltip({
  active,
  payload,
  label,
  className,
}: CustomTooltipProps) {
  if (active && payload && payload.length) {
    return (
      <div
        className={cn(
          "rounded-lg border border-gray-200 bg-white p-2 text-sm shadow-lg dark:border-gray-700 dark:bg-gray-50",
          className
        )}
      >
        <p className="font-medium text-gray-900 dark:text-gray-700">{label}</p>
        <p className="text-gray-500 dark:text-gray-600">
          {payload[0].value?.toLocaleString()}
        </p>
      </div>
    );
  }

  return null;
}

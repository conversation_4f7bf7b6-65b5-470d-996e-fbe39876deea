import { TokenCaptureResult } from "@/types/services";

export abstract class BaseTokenCollector {
  protected abstract providerId: string;
  protected abstract backendUrl: string;

  protected log(message: string, level: "info" | "warn" | "error" = "info") {
    const timestamp = new Date().toISOString();
    const prefix = `[${this.providerId.toUpperCase()}]`;

    switch (level) {
      case "error":
        console.error(`${timestamp} ${prefix} ❌ ${message}`);
        break;
      case "warn":
        console.warn(`${timestamp} ${prefix} ⚠️ ${message}`);
        break;
      default:
        console.log(`${timestamp} ${prefix} ℹ️ ${message}`);
    }
  }

  // Método abstrato que deve ser implementado pelas classes filhas
  abstract captureAllTokens(): Promise<TokenCaptureResult[]>;
}

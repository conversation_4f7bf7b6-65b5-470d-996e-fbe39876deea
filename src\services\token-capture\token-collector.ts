import {
  ProviderTokenConfig,
  TokenCaptureRequest,
  TokenCaptureResponse,
  TokenCaptureResult,
  TokenCaptureStatusResponse,
} from "@/types/services";
import { BaseTokenCollector } from "./base-token-collector";

export class TokenCollector extends BaseTokenCollector {
  protected providerId: string;
  protected backendUrl: string;
  private config: ProviderTokenConfig;

  constructor(config: ProviderTokenConfig) {
    super();
    this.config = config;
    this.providerId = config.providerId;
    this.backendUrl =
      import.meta.env.VITE_APP_TOKEN_SCRAPER_URL || "http://localhost:3002";
  }

  // Chama o serviço externo para capturar token de todos os acessos
  async captureAllTokens(): Promise<TokenCaptureResult[]> {
    this.log("🔄 Iniciando captura de tokens para todos os painéis...");

    try {
      const results: TokenCaptureResult[] = [];

      // Itera sobre todos os painéis configurados
      for (const providerAccess of this.config.providerAccess) {
        try {
          this.log(
            `📝 Capturando token para painel: ${providerAccess.panelName}`
          );

          // Chama o serviço externo para capturar o token
          const tokenResponse = await this.captureTokenFromService(this.config);

          if (tokenResponse.success && tokenResponse.token) {
            // Cria resultado de sucesso
            const result: TokenCaptureResult = {
              id: `${this.providerId}_${providerAccess.panelName}_${Date.now()}`,
              jobId: tokenResponse.jobId || "", // Usa o jobId retornado pelo monitoramento
              providerId: this.providerId,
              panelName: providerAccess.panelName,
              token: tokenResponse.token,
              cookies: tokenResponse.cookies || [],
              headers: tokenResponse.headers || {},
              timestamp: new Date(),
              success: true,
              error: undefined, // Não há erro em caso de sucesso
              expiresAt: tokenResponse.expiresAt || undefined,
            };

            // Salva resultado no Firestore
            await this.saveTokenCaptureResult(result);

            results.push(result);
            this.log(
              `✅ Token capturado com sucesso para ${providerAccess.panelName}`
            );
          } else {
            // Cria resultado de falha
            const result: TokenCaptureResult = {
              id: `${this.providerId}_${providerAccess.panelName}_${Date.now()}`,
              jobId: tokenResponse.jobId || "", // Usa o jobId retornado pelo monitoramento
              providerId: this.providerId,
              panelName: providerAccess.panelName,
              token: "",
              timestamp: new Date(),
              success: false,
              error: tokenResponse.error || "Falha na captura de token",
            };

            // Salva resultado no Firestore
            await this.saveTokenCaptureResult(result);

            results.push(result);
            this.log(
              `❌ Falha na captura de token para ${providerAccess.panelName}: ${tokenResponse.error}`
            );
          }
        } catch (error) {
          this.log(
            `❌ Erro ao capturar token para ${providerAccess.panelName}: ${error}`,
            "error"
          );

          // Cria resultado de erro
          const result: TokenCaptureResult = {
            id: `${this.providerId}_${providerAccess.panelName}_${Date.now()}`,
            jobId: "", // Não há jobId em caso de erro na chamada inicial
            providerId: this.providerId,
            panelName: providerAccess.panelName,
            token: "",
            timestamp: new Date(),
            success: false,
            error: error instanceof Error ? error.message : String(error),
          };

          // Salva resultado no Firestore
          await this.saveTokenCaptureResult(result);

          results.push(result);
        }
      }

      this.log(
        `📊 Captura concluída: ${results.filter((r) => r.success).length}/${results.length} tokens capturados`
      );
      return results;
    } catch (error) {
      this.log(`❌ Erro na captura de tokens: ${error}`, "error");
      throw error;
    }
  }

  // Chama o serviço externo para capturar token de um acesso
  async captureTokenFromService(
    providerDataAccess: ProviderTokenConfig
  ): Promise<TokenCaptureResult> {
    const request: TokenCaptureRequest = {
      providerSlug: providerDataAccess.providerSlug,
      panelName: providerDataAccess.providerAccess[0].panelName,
      panelUrl: providerDataAccess.providerAccess[0].panelUrl,
      username: providerDataAccess.providerAccess[0].credentials.username,
      password: providerDataAccess.providerAccess[0].credentials.password,
    };

    try {
      // 1. Inicia o job na API externa
      this.log(
        `🚀 Iniciando job de captura de token para ${request.panelName}...`
      );

      const response = await fetch(`${this.backendUrl}/api/capture-token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const jobResponse: TokenCaptureResponse = await response.json();

      // Nova estrutura: verifica success e jobId
      if (!jobResponse.success || !jobResponse.jobId) {
        throw new Error("API não retornou jobId válido");
      }

      this.log(
        `📋 Job iniciado com ID: ${jobResponse.jobId} - Status: ${jobResponse.status}`
      );

      // 2. Monitora o progresso do job
      const result = await this.monitorJobProgress(
        jobResponse.jobId,
        request.panelName
      );

      return result;
    } catch (error) {
      throw new Error(`Falha na comunicação com serviço externo: ${error}`);
    }
  }

  // Monitora o progresso de um job até sua conclusão
  private async monitorJobProgress(
    jobId: string,
    panelName: string
  ): Promise<TokenCaptureResult> {
    const maxAttempts = 60; // Máximo 5 minutos (5s * 60)
    const pollInterval = 5000; // 5 segundos
    let attempts = 0;

    this.log(`⏳ Monitorando job ${jobId} para painel ${panelName}...`);

    while (attempts < maxAttempts) {
      try {
        // Verifica status do job usando o novo endpoint
        const statusResponse = await fetch(
          `${this.backendUrl}/api/capture-status/${jobId}`
        );

        if (!statusResponse.ok) {
          throw new Error(
            `HTTP ${statusResponse.status}: ${statusResponse.statusText}`
          );
        }

        const response: TokenCaptureStatusResponse =
          await statusResponse.json();

        // Nova estrutura: response.status.status em vez de response.status
        if (!response.success || !response.status) {
          throw new Error("Resposta da API inválida");
        }

        const jobStatus = response.status;

        this.log(
          `📊 Job ${jobId}: ${jobStatus.status} (check ${attempts + 1}/${maxAttempts})`
        );

        // Verifica se o job foi concluído
        if (jobStatus.status === "completed") {
          this.log(`✅ Job ${jobId} concluído com sucesso!`);

          // Cria resultado de sucesso
          const result: TokenCaptureResult = {
            id: `${this.providerId}_${panelName}_${Date.now()}`,
            jobId: jobId,
            providerId: this.providerId,
            panelName: panelName,
            token: jobStatus.result?.token || "",
            cookies: jobStatus.result?.cookies || [],
            headers: {}, // Headers não estão disponíveis na nova API
            timestamp: new Date(),
            success: true,
            error: undefined, // Não há erro em caso de sucesso
            expiresAt: jobStatus.result?.expiresAt
              ? new Date(jobStatus.result.expiresAt)
              : undefined,
          };

          // Salva resultado no Firestore
          await this.saveTokenCaptureResult(result);

          return result;
        }

        // Verifica se o job falhou
        if (jobStatus.status === "failed") {
          this.log(`❌ Job ${jobId} falhou: ${jobStatus.error}`);

          // Cria resultado de falha
          const result: TokenCaptureResult = {
            id: `${this.providerId}_${panelName}_${Date.now()}`,
            jobId: jobId,
            providerId: this.providerId,
            panelName: panelName,
            token: "",
            timestamp: new Date(),
            success: false,
            error: jobStatus.error || "Job falhou",
          };

          // Salva resultado no Firestore
          await this.saveTokenCaptureResult(result);

          return result;
        }

        // Job ainda está em processamento, aguarda e tenta novamente
        // Novo estado: 'running' em vez de 'processing'
        if (jobStatus.status === "running" || jobStatus.status === "pending") {
          this.log(`⏳ Job ${jobId} ainda em processamento, aguardando...`);
          await this.sleep(pollInterval);
          attempts++;
          continue;
        }

        // Status desconhecido
        this.log(`⚠️ Status desconhecido do job ${jobId}: ${jobStatus.status}`);
        attempts++;
        continue;
      } catch (error) {
        this.log(
          `❌ Erro ao verificar status do job ${jobId}: ${error}`,
          "error"
        );
        attempts++;

        // Se for erro de rede, aguarda um pouco mais
        if (attempts < maxAttempts) {
          await this.sleep(pollInterval * 2);
        }
      }
    }

    // Timeout - job não foi concluído no tempo esperado
    this.log(
      `⏰ Timeout: Job ${jobId} não foi concluído em ${(maxAttempts * pollInterval) / 1000} segundos`
    );

    const result: TokenCaptureResult = {
      id: `${this.providerId}_${panelName}_${Date.now()}`,
      jobId: jobId,
      providerId: this.providerId,
      panelName: panelName,
      token: "",
      timestamp: new Date(),
      success: false,
      error: `Timeout: Job não foi concluído em ${(maxAttempts * pollInterval) / 1000} segundos`,
    };

    // Salva resultado de timeout no Firestore
    await this.saveTokenCaptureResult(result);

    return result;
  }

  // Salva resultado da captura de token no Firestore
  private async saveTokenCaptureResult(
    result: TokenCaptureResult
  ): Promise<void> {
    try {
      const { addDoc, collection } = await import("firebase/firestore");
      const { db } = await import("@/lib/firebase");

      // Prepara dados para salvar no Firestore (sem campos undefined)
      const dataToSave: TokenCaptureResult = {
        id: result.id,
        jobId: result.jobId,
        providerId: result.providerId,
        providerDataAccessId: this.config.id,
        panelName: result.panelName,
        token: result.token,
        cookies: result.cookies || [],
        headers: result.headers || {},
        timestamp: result.timestamp,
        success: result.success,
        expiresAt: result.expiresAt,
      };

      // Adiciona campos opcionais apenas se não forem undefined
      if (result.error !== undefined) {
        dataToSave.error = result.error;
      }
      if (result.expiresAt !== undefined) {
        dataToSave.expiresAt = result.expiresAt;
      }

      // Salva na coleção token_capture_results
      const docRef = await addDoc(collection(db, "token_capture_results"), dataToSave);

      this.log(
        `💾 Resultado salvo no Firestore: ${result.success ? "Sucesso" : "Falha"} - Doc ID: ${docRef.id}`
      );
      console.log('🔍 Debug - Dados salvos no Firestore:', {
        docId: docRef.id,
        providerDataAccessId: dataToSave.providerDataAccessId,
        success: dataToSave.success,
        timestamp: dataToSave.timestamp
      });
    } catch (error) {
      this.log(`❌ Erro ao salvar resultado no Firestore: ${error}`, "error");
      // Não falha a operação se não conseguir salvar
    }
  }

  // Função auxiliar para aguardar
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

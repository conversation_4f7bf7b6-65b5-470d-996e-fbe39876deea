import { addDoc, collection, getDocs, writeBatch } from "firebase/firestore";
import { db } from "./firebase";

// ============================================================================
// DADOS REAIS IMPORTADOS DO SISTEMA EM PRODUÇÃO
// ============================================================================

// Providers (dados reais - IDs automáticos)
const providers = [
  {
    name: "FoxESS",
    provider_slug: "foxess",
    api_url: "https://api.foxess.com",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    provider_slug: "fronius",
    api_url: "https://api.fronius.com",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Goodwe",
    provider_slug: "goodwe",
    api_url: "https://us.semsportal.com",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "WEG",
    provider_slug: "weg",
    api_url: "https://api.weg.com",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Campus (dados reais - IDs automáticos)
const campus = [
  {
    name: "Campus Alvorada",
    city: "Alvorada",
    lat: -30.00038,
    long: -51.051439,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Bento Gonçalves",
    city: "Bento Gonçalves",
    lat: -29.1637544,
    long: -51.522695,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Canoas",
    city: "Canoas",
    lat: -29.89985,
    long: -51.150555,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Caxias do Sul",
    city: "Caxias do Sul",
    lat: -29.1392721,
    long: -51.1716015,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Farroupilha",
    city: "Farroupilha",
    lat: -29.202427,
    long: -51.349101,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Rio Grande",
    city: "Rio Grande",
    lat: -32.0402001,
    long: -52.0888947,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Rolante",
    city: "Rolante",
    lat: -29.651859,
    long: -50.576752,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Sertão",
    city: "Sertão",
    lat: -28.0455574,
    long: -52.2753086,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    name: "Campus Vacaria",
    city: "Vacaria",
    lat: -28.4553184,
    long: -50.9538192,
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Usinas (dados reais - IDs automáticos, mas provider_usina_id mantido)
const usinas = [
  {
    name: "Goodwe Alvorada",
    capacity: 55.44,
    lat: -30.00038,
    long: -51.051439,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "abd2c0a5-b97a-4197-9a69-8861452c0868", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Alvorada",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Bento",
    capacity: 55.44,
    lat: -29.1637544,
    long: -51.522695,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "120e99f1-26ef-405d-8941-84e93be92115", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Bento Gonçalves",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Canoas",
    capacity: 55.44,
    lat: -29.89985,
    long: -51.150555,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "77eb10f5-905b-46b3-a973-fa8c7a8a8e5d", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Canoas",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Caxias",
    capacity: 55.44,
    lat: -29.1392721,
    long: -51.1716015,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "ecae608e-b02a-49a7-b488-cd8621789fde", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Caxias do Sul",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Farroupilha",
    capacity: 55.44,
    lat: -29.202427,
    long: -51.349101,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "f9907f90-015d-4084-a5e0-2c9ebd786c9e", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Farroupilha",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Rio Grande",
    capacity: 55.44,
    lat: -32.0402001,
    long: -52.0888947,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "1a7ae802-fa88-4e62-b0d8-da97451226b3", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Rio Grande",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Rolante",
    capacity: 55.44,
    lat: -29.651859,
    long: -50.576752,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "64e45955-6e57-48a0-b702-df434d0eb363", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Rolante",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Sertão",
    capacity: 55.44,
    lat: -28.0455574,
    long: -52.2753086,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "f0ad35c1-e282-4792-bfdc-09bd4a88dcdf", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Sertão",
    providerSlug: "goodwe",
  },
  {
    name: "Goodwe Vacaria",
    capacity: 50,
    lat: -28.4553184,
    long: -50.9538192,
    model: "Goodwe Inverter",
    module_qtd: 1,
    eficiency: 0.9,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "4c793d22-fa84-4e2e-940b-7ca46698ee4e", // MANTER este valor
    provider_access: "default",
    status: "ACTIVE",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    campusName: "Campus Vacaria",
    providerSlug: "goodwe",
  },
];

// ProvidersDataAccess (dados reais - IDs automáticos)
const providersDataAccess = [
  {
    provider_slug: "goodwe",
    provider_access: "default",
    panel_url: "https://www.semsportal.com/home/<USER>",
    panel_user: "<EMAIL>",
    panel_password: "Goodwe2018",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    providerSlug: "goodwe",
  },
  {
    provider_slug: "weg",
    provider_access: "default",
    panel_url: "https://sun.weg.net/sign-in",
    panel_user: "<EMAIL>",
    panel_password: "12345678",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    providerSlug: "weg",
  },
  {
    provider_slug: "foxess",
    provider_access: "Erechim",
    panel_url: "https://www.foxesscloud.com/login",
    panel_user: "instituto.erechim",
    panel_password: "solar123",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    providerSlug: "foxess",
  },
  {
    provider_slug: "foxess",
    provider_access: "Restinga",
    panel_url: "https://www.foxesscloud.com/login",
    panel_user: "instituto.restinga",
    panel_password: "solar123",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    providerSlug: "foxess",
  },
  {
    provider_slug: "foxess",
    provider_access: "RioGrande",
    panel_url: "https://www.foxesscloud.com/login",
    panel_user: "instituto.federalrs",
    panel_password: "solar123",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    providerSlug: "foxess",
  },
  {
    provider_slug: "fronius",
    provider_access: "default",
    panel_url: "https://www.solarweb.com/PvSystems/Widgets",
    panel_user: "<EMAIL>",
    panel_password: "Efall123*ifrscanoas",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    providerSlug: "fronius",
  },
];

// ============================================================================
// FUNÇÃO PRINCIPAL DE SEED UNIFICADA (COM IDs AUTOMÁTICOS)
// ============================================================================

export async function seedFirebase() {
  try {
    console.log("🌱 Iniciando seed com dados reais do Firebase...");
    console.log("🔍 Verificando conexão com emulador...");

    // Teste de conexão
    try {
      collection(db, "test");
      console.log("✅ Conexão com Firestore estabelecida");
    } catch (connectionError) {
      console.error("❌ Erro de conexão com Firestore:", connectionError);
      throw new Error(
        "Não foi possível conectar ao Firestore. Verifique se o emulador está rodando."
      );
    }

    // 1. Criar providers com IDs automáticos
    console.log("📝 Criando providers...");
    const providerRefs: Record<string, { id: string }> = {};

    for (const provider of providers) {
      try {
        const providerRef = await addDoc(collection(db, "providers"), provider);
        providerRefs[provider.provider_slug] = providerRef;
        console.log(
          `✅ Provider criado: ${provider.name} (ID: ${providerRef.id})`
        );
      } catch (error) {
        console.error(`❌ Erro ao criar provider ${provider.name}:`, error);
        throw error;
      }
    }

    // 2. Criar campus com IDs automáticos
    console.log("📝 Criando campus...");
    const campusRefs: Record<string, { id: string }> = {};

    for (const camp of campus) {
      try {
        const campusRef = await addDoc(collection(db, "campus"), camp);
        campusRefs[camp.name] = campusRef;
        console.log(`✅ Campus criado: ${camp.name} (ID: ${campusRef.id})`);
      } catch (error) {
        console.error(`❌ Erro ao criar campus ${camp.name}:`, error);
        throw error;
      }
    }

    // 3. Criar providersDataAccess com referências aos providers
    console.log("📝 Criando acessos de providers...");
    for (const dataAccess of providersDataAccess) {
      try {
        const providerRef = providerRefs[dataAccess.providerSlug];
        if (!providerRef) {
          console.warn(
            `⚠️ Provider não encontrado para slug: ${dataAccess.providerSlug}`
          );
          continue;
        }

        const dataAccessWithProvider = {
          ...dataAccess,
          provider_id: providerRef.id, // Adiciona referência ao provider criado
        };

        await addDoc(
          collection(db, "providersDataAccess"),
          dataAccessWithProvider
        );
        console.log(
          `✅ Acesso criado para ${dataAccess.providerSlug} - ${dataAccess.provider_access}`
        );
      } catch (error) {
        console.error(
          `❌ Erro ao criar acesso ${dataAccess.providerSlug}:`,
          error
        );
        throw error;
      }
    }

    // 4. Criar usinas com referências aos campus e providers
    console.log("📝 Criando usinas...");
    for (const usina of usinas) {
      try {
        const campusRef = campusRefs[usina.campusName];
        const providerRef = providerRefs[usina.providerSlug];

        if (!campusRef) {
          console.warn(`⚠️ Campus não encontrado: ${usina.campusName}`);
          continue;
        }
        if (!providerRef) {
          console.warn(`⚠️ Provider não encontrado: ${usina.providerSlug}`);
          continue;
        }

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { campusName, providerSlug, ...usinaData } = usina;

        const usinaWithRefs = {
          ...usinaData,
          campusId: campusRef.id, // Referência ao campus
          providerId: providerRef.id, // Referência ao provider
          provider_slug: providerSlug, // Mantém o slug
          // provider_usina_id já está presente e deve ser mantido
        };

        const usinaRef = await addDoc(collection(db, "usinas"), usinaWithRefs);
        console.log(`✅ Usina criada: ${usina.name} (ID: ${usinaRef.id})`);
      } catch (error) {
        console.error(`❌ Erro ao criar usina ${usina.name}:`, error);
        throw error;
      }
    }

    console.log("🎉 Seed concluído com sucesso!");
    console.log(`📊 Resumo:`);
    console.log(`  • ${providers.length} Providers criados`);
    console.log(`  • ${campus.length} Campus criados`);
    console.log(
      `  • ${providersDataAccess.length} Acessos de provider criados`
    );
    console.log(`  • ${usinas.length} Usinas criadas`);

    return {
      success: true,
      message: `Seed concluído: ${providers.length} providers, ${campus.length} campus, ${usinas.length} usinas`,
      totalProviders: providers.length,
      totalCampus: campus.length,
      totalUsinas: usinas.length,
      totalDataAccess: providersDataAccess.length,
    };
  } catch (error) {
    console.error("❌ Erro no seed:", error);
    return {
      success: false,
      message: `Erro no seed: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
    };
  }
}

// ============================================================================
// FUNÇÕES AUXILIARES
// ============================================================================

// Função para verificar se os dados já existem
export async function checkDataExists() {
  try {
    const providersSnapshot = await getDocs(collection(db, "providers"));
    const campusSnapshot = await getDocs(collection(db, "campus"));
    const usinasSnapshot = await getDocs(collection(db, "usinas"));

    return {
      providersExist: !providersSnapshot.empty,
      campusExist: !campusSnapshot.empty,
      usinasExist: !usinasSnapshot.empty,
      hasData:
        !providersSnapshot.empty &&
        !campusSnapshot.empty &&
        !usinasSnapshot.empty,
    };
  } catch (error) {
    console.error("Erro ao verificar dados:", error);
    return {
      providersExist: false,
      campusExist: false,
      usinasExist: false,
      hasData: false,
    };
  }
}

// Função para limpar todos os dados (apenas para desenvolvimento)
export async function clearFirebase() {
  try {
    console.log("🧹 Limpando todos os dados do Firebase...");

    // Deletar todos os documentos de cada coleção
    const collections = [
      "providers",
      "providersDataAccess",
      "campus",
      "usinas",
    ];
    for (const collectionName of collections) {
      const snapshot = await getDocs(collection(db, collectionName));
      const batch = writeBatch(db);

      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log(`✅ ${collectionName} limpo`);
    }
    console.log("✅ Dados do Firebase limpos!");
    return { success: true, message: "Banco limpo com sucesso!" };
  } catch (error) {
    console.error("❌ Erro ao limpar dados:", error);
    return {
      success: false,
      message: `Erro ao limpar: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
    };
  }
}

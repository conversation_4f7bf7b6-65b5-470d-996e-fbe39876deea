import { Suspense, lazy } from "react";
import { Route, Routes } from "react-router-dom";

// Páginas com lazy loading
const CampusListPage = lazy(() => import("./pages/CampusListPage"));
const CampusCreatePage = lazy(() => import("./pages/CampusCreatePage"));
const CampusEditPage = lazy(() => import("./pages/CampusEditPage"));
const CampusDetailPage = lazy(() => import("./pages/CampusDetailPage"));
const UsinaCreatePage = lazy(() => import("./pages/UsinaCreatePage"));
const UsinaEditPage = lazy(() => import("./pages/UsinaEditPage"));

export default function CampusRouter() {
  return (
    <Suspense fallback={<div className="p-4">Carregando...</div>}>
      <Routes>
        <Route path="/" element={<CampusListPage />} />
        <Route path="/create" element={<CampusCreatePage />} />
        <Route path="/edit/:id" element={<CampusEditPage />} />
        <Route path="/detail/:id" element={<CampusDetailPage />} />
        <Route path="/:campusId/usina/create" element={<UsinaCreatePage />} />
        <Route path="/:campusId/usina/edit/:id" element={<UsinaEditPage />} />
      </Routes>
    </Suspense>
  );
}

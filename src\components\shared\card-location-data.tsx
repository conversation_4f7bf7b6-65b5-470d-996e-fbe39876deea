import { CampusData, DataEnergyNetwork } from "@/types";
import { cn } from "@/utils/merge-classes";
import { ReactNode } from "react";

export default function CardLocationData({
  campusData,
  dataEnergyNetwork,
}: {
  campusData: CampusData;
  dataEnergyNetwork: DataEnergyNetwork[];
}) {
  const classes = {
    card: `
    flex relative
    p-5 justify-between
    rounded-xl 
    border-gray-100
    `,
    cardBg1: `bg-gradient-to-b from-gray-50 to-gray-200`,
    cardBg2: `bg-gradient-to-b from-orange-50 to-orange-200`,
  };

  const CardData = ({
    children,
    className,
  }: {
    children: ReactNode;
    className?: string;
  }) => {
    return <div className={cn(classes.card, className)}>{children}</div>;
  };

  return (
    <div className="h-fit rounded-xl">
      {campusData.photo ? (
        <img
          src={campusData.photo}
          alt={`${campusData.name} via satélite`}
          className="w-full relative h-80 object-cover rounded-xl z-11"
        />
      ) : (
        <div className="w-full relative h-80 object-cover rounded-xl z-11 bg-gray-200">
          <iframe
            src="https://www.rainviewer.com/map.html?loc=-29.038,-51.0082,5.99990344556713&oFa=1&oCS=1&c=3&o=83&lm=1&layer=sat-rad&sm=1&ts=1"
            style={{ border: "0", width: "100%", height: "100%" }}
            allowFullScreen
          ></iframe>
        </div>
      )}
      <CardData className={cn(classes.cardBg1, "flex-row pt-8 -mt-5 z-10")}>
        <div className="flex flex-col">
          <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
            Capacidade Instalada
          </span>
          <span className="font-data font-extrabold italic text-slate-600 text-2xl 2xl:text-3xl">
            {campusData.capacity} kWp
          </span>
        </div>
        <div className="flex flex-col w-[50%] gap-2">
          <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
            Inversores
          </span>
          {campusData.inverter.map((item) => {
            return (
              <span className="font-data font-semibold text-xl italic text-slate-600 border-l-2 border-slate-400 pl-2">
                #{item.inverter_id.slice(0, 7)}
                <br />
                {item.inverter_model}
                {item.inverter_capacity && (
                  <>
                    <br />
                    {item.inverter_capacity} kWp
                  </>
                )}
              </span>
            );
          })}
        </div>
      </CardData>
      {campusData.energy_company && (
        <CardData className={cn(classes.cardBg2, "flex-col mt-4 z-8")}>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Companhia Elétrica:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-2xl 2xl:text-3xl">
              {campusData.energy_company}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Medições do Último Período:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].period}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Energia Injetada TUSD:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].energy_injected_tusd}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Energia Injetada TE:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].energy_injected_te}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Energia Ponta TUSD:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].energy_ponta_tusd}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Energia Ponta TE:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].energy_ponta_te}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Energia FPonta TUSD:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].energy_fponta_tusd}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Energia FPonta TE:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].energy_fponta_te}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Demanda Ponta TUSD:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].demand_ponta_tusd}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Demanda FPonta TUSD:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].demand_fponta_tusd}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Consumo Reativo FPonta:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].reactive_consumption_fponta}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Consumo Reativo Ponta:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].reactive_consumption_ponta}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Total da Conta:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].total_account.toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Valor kW/h:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].kwh_value.toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })}
            </span>
          </div>
          <div className="flex flex-row gap-2">
            <span className="flex items-center font-display text-gray-500 text-sm 2xl:text-base">
              Valor Financeiro Gerado:
            </span>
            <span className="font-data font-semibold italic text-slate-600 text-xl">
              {dataEnergyNetwork[0].financial_value_generated.toLocaleString(
                "pt-BR",
                {
                  style: "currency",
                  currency: "BRL",
                }
              )}
            </span>
          </div>
          <div className="flex flex-row font-display text-gray-500 gap-2 mt-2">
            Consumos Anteriores:
          </div>
        </CardData>
      )}
      <div className="flex h-60 mt-4 font-display font-semibold p-6 rounded-xl flex-col @container [background:linear-gradient(29deg,#b1c1d1_12.96%,#f0f6ff_94.88%)]">
        Gráfico Histórico de Consumo
      </div>
    </div>
  );
}

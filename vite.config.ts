import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@mocks": path.resolve(__dirname, "./src/mocks"),
      "@types": path.resolve(__dirname, "./src/types"),
      "@shared": path.resolve(__dirname, "./src/components/shared"),
    },
  },
  server: {
    port: 3001,
    open: true,
    proxy: {
      // Proxy para o SEMS Portal (Goodwe)
      '/api/sems': {
        target: 'https://us.semsportal.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/sems/, '/api'),
        secure: true,
        headers: {
          'Origin': 'https://us.semsportal.com',
          'Referer': 'https://us.semsportal.com/',
        },
      },
      // Proxy para o painel de login
      '/sems-portal': {
        target: 'https://www.semsportal.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/sems-portal/, ''),
        secure: true,
        headers: {
          'Origin': 'https://www.semsportal.com',
          'Referer': 'https://www.semsportal.com/',
        },
      },
    },
  },
});

import ProviderSelect from "@/components/shared/ProviderSelect";
import WidgetCard from "@/components/shared/widget-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type { UpdateUsinaData, Usina } from "@/types";
import { parseInteger, parseNumber, validateCapacity, validateRequiredFields } from "@/utils";
import { ArrowLeft, Check } from "lucide-react";
import { FormEvent, useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useProviderAccess } from "../../providers/hooks/useProviderAccess";
import { useUsinas } from "../hooks/useUsinas";

export default function UsinaEditPage() {
  const { campusId, id } = useParams<{ campusId: string; id: string }>();
  const navigate = useNavigate();
  const { usinas, updateUsina } = useUsinas(campusId);
  const [usina, setUsina] = useState<Usina | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState<UpdateUsinaData>({
    name: "",
    capacity: 0,
    lat: 0,
    long: 0,
    model: "",
    module_qtd: 0,
    eficiency: 0,
    area_m2: 0,
    azimut: 0,
    provider_usina_id: "",
    campusId: campusId || "",
    providerId: "",
    provider_access: "default",
    status: "ACTIVE",
  });

  const { accessList } = useProviderAccess(formData.providerId);

  useEffect(() => {
    if (!id) return;
    const found = usinas.find((u) => u.id === id) || null;
    if (found) {
      setUsina(found);
      setFormData({
        name: found.name,
        capacity: found.capacity,
        lat: found.lat,
        long: found.long,
        model: found.model,
        module_qtd: found.module_qtd,
        eficiency: found.eficiency,
        area_m2: found.area_m2,
        azimut: found.azimut,
        provider_usina_id: found.provider_usina_id,
        campusId: found.campusId,
        providerId: found.providerId,
        provider_access: found.provider_access || "default",
        status: found.status,
      });
    }
  }, [id, usinas]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const requiredErrors = validateRequiredFields(formData, [
      "name",
      "capacity",
      "provider_usina_id",
      "providerId",
    ]);
    Object.assign(newErrors, requiredErrors);
    const capacityError = validateCapacity(formData.capacity);
    if (capacityError) newErrors.capacity = capacityError;
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!validateForm() || !id) return;
    setLoading(true);
    try {
      await updateUsina(id, formData);
      navigate(`/admin/campus/detail/${campusId}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UpdateUsinaData, value: string | number) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field as string]) setErrors((prev) => ({ ...prev, [field as string]: undefined as any }));
  };

  if (!usina) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Carregando usina...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to={`/admin/campus/detail/${campusId}`}>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Editar Usina</h1>
        </div>
      </div>

      <WidgetCard>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Nome *</label>
              <Input value={formData.name} onChange={(e) => handleInputChange("name", e.target.value)} />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Provedor *</label>
              <ProviderSelect value={formData.providerId} onChange={(v) => handleInputChange("providerId", v)} />
              {errors.providerId && <p className="text-red-500 text-sm mt-1">{errors.providerId}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Painel</label>
              <select
                className="w-full border rounded h-9 px-3 text-sm"
                value={formData.provider_access || "default"}
                onChange={(e) => handleInputChange("provider_access", e.target.value)}
                disabled={!formData.providerId}
              >
                <option value="default">default</option>
                {accessList
                  ?.filter((a) => a.provider_access !== "default")
                  .map((a) => (
                    <option key={a.id} value={a.provider_access}>
                      {a.provider_access}
                    </option>
                  ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Capacidade (kW) *</label>
              <Input type="number" step="0.01" value={formData.capacity} onChange={(e) => handleInputChange("capacity", parseNumber(e.target.value))} />
              {errors.capacity && <p className="text-red-500 text-sm mt-1">{errors.capacity}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Modelo</label>
              <Input value={formData.model} onChange={(e) => handleInputChange("model", e.target.value)} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Qtd. Módulos</label>
              <Input type="number" value={formData.module_qtd} onChange={(e) => handleInputChange("module_qtd", parseInteger(e.target.value))} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Eficiência (%)</label>
              <Input type="number" step="0.01" value={formData.eficiency} onChange={(e) => handleInputChange("eficiency", parseNumber(e.target.value))} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Área (m²)</label>
              <Input type="number" step="0.01" value={formData.area_m2} onChange={(e) => handleInputChange("area_m2", parseNumber(e.target.value))} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Azimute (°)</label>
              <Input type="number" step="0.01" value={formData.azimut} onChange={(e) => handleInputChange("azimut", parseNumber(e.target.value))} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">ID Interno do Provedor *</label>
              <Input value={formData.provider_usina_id} onChange={(e) => handleInputChange("provider_usina_id", e.target.value)} />
            </div>
          </div>

          <div className="flex items-center gap-4 pt-4">
            <Button type="submit" disabled={loading} className="flex items-center gap-2">
              <Check className="h-4 w-4" />
              {loading ? "Salvando..." : "Salvar Usina"}
            </Button>
            <Link to={`/admin/campus/detail/${campusId}`}>
              <Button variant="outline" disabled={loading}>Cancelar</Button>
            </Link>
          </div>
        </form>
      </WidgetCard>
    </div>
  );
}



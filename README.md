# MonitoraLux

Sistema de monitoramento de energia elétrica e geração de energia solar.

## Stack Tecnológico

### Core

- **React 19** + **TypeScript** + **Vite**
- **AWS Amplify Gen 2** (Backend/GraphQL)
- **Tailwind CSS 4** (Styling)

### UI/UX

- **shadcn/ui** (Design System)
- **Radix UI** (Primitivos acessíveis)
- **class-variance-authority** (Variantes de componentes)
- **Lucide React** (Ícones)
- **Framer Motion** (Animações)
- **next-themes** (Dark mode)

### Estado e Dados

- **TanStack Query** (Server state)
- **Zustand** (Client state)
- **React Hook Form** + **Zod** (Formulários)
- **React Table** (Tabelas)

### Utilitários

- **Axios** (HTTP client)
- **Day.js** (Datas)
- **Recharts** (Gráficos)
- **Sonner** (Toasts)

## Pré-requisitos

- Node.js 20+
- npm/yarn/pnpm

## Design System

### Cores Customizadas

- Sistema baseado em **OKLCH** para cores consistentes
- Suporte completo a **dark mode**
- Variáveis CSS customizadas (`--primary-dark`, etc.)

### Componentes

- Baseados em **shadcn/ui** com customizações
- Variantes tipadas com **cva**
- Acessibilidade com **Radix UI**

## Estrutura do Projeto

```
src/
  ├── components/       # Componentes reutilizáveis
  │   ├── ui/          # Design system (shadcn/ui)
  │   ├── layout/      # Componentes de layout
  │   └── shared/      # Componentes compartilhados
  ├── pages/           # Páginas da aplicação
  │   ├── admin/       # Interfaces administrativas
  │   ├── campus/      # Páginas de campus
  │   ├── dashboard/   # Dashboard principal
  │   └── faq/         # FAQ
  ├── hooks/           # Hooks customizados
  ├── services/        # Serviços de API
  ├── types/           # Definições TypeScript
  ├── utils/           # Utilitários compartilhados
  ├── mocks/           # Dados de mock
  ├── global.css       # Estilos globais + variáveis CSS
  └── main.tsx         # Entry point
```

import { Suspense, lazy } from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";

// Componentes
import { Layout } from "@/components/layout/Layout";

// Páginas com lazy loading
const DashboardPage = lazy(() => import("@/pages/dashboard"));
const CampusDashboardPage = lazy(() => import("@/pages/campus"));
const FAQPage = lazy(() => import("@/pages/faq"));

// Páginas de Admin com lazy loading
const AdminRouter = lazy(() => import("@/pages/admin"));

function App() {
  return (
    <BrowserRouter>
      <Layout>
        <Suspense fallback={<div className="p-4">Carregando...</div>}>
          <Routes>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/dashboard/:id" element={<CampusDashboardPage />} />
            <Route path="/faq" element={<FAQPage />} />
            <Route path="/admin/*" element={<AdminRouter />} />
          </Routes>
        </Suspense>
      </Layout>
    </BrowserRouter>
  );
}

export default App;

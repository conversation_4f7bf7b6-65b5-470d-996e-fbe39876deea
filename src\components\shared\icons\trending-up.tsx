import { SVGProps } from 'react';

const TrendingUpIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M15.22 6.268a.75.75 0 01.968-.432l5.942 2.28a.75.75 0 01.431.97l-2.28 5.94a.75.75 0 11-1.4-.537l1.63-4.251-1.086.484a11.2 11.2 0 00-5.45 *********** 0 01-1.199.19L9 12.312l-6.22 6.22a.75.75 0 11-1.06-1.06l6.75-6.75a.75.75 0 011.06 0l3.606 3.606a12.695 12.695 0 015.68-4.974l1.086-.484-4.251-1.631a.75.75 0 01-.432-.97z"
      clipRule="evenodd"
    />
  </svg>
);

export default TrendingUpIcon; 
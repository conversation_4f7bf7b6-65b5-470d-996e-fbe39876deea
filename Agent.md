# 🤖 Agent - Sistema de Monitoramento de Energia

## 📋 Visão Geral

Sistema híbrido de monitoramento e coleta de dados de energia solar que combina frontend React com backend Node.js para captura de tokens e coleta de dados via APIs externas.

## 🏗️ Arquitetura

### **Frontend (React + Vite)**
- **Interface**: Componentes React para monitoramento em tempo real
- **Estado**: Gerenciamento de estado com hooks (useState, useEffect)
- **Firebase**: Integração com Firestore para persistência de dados
- **APIs**: Comunicação com backend Node.js para operações assíncronas

### **Backend (Node.js)**
- **Token Capture**: Serviço externo para captura de tokens de autenticação
- **Data Collection**: Coleta de dados via APIs dos providers
- **Job Management**: Sistema de jobs assíncronos com polling

## 🔧 Componentes Principais

### **1. ProviderStatus.tsx**
**Localização**: `src/pages/admin/components/ProviderStatus.tsx`

**Funcionalidades**:
- Exibe status em tempo real dos providers
- Botões para captura manual de tokens e coleta de dados
- Indicadores visuais de progresso (spinners, status badges)
- Formatação de datas com hora completa (DD/MM/AAAA HH:MM)

**Estados dos Tokens**:
- `valid`: Token válido e não expirado
- `expired`: Token expirado
- `missing`: Nenhum token encontrado

**Estados dos Providers**:
- `idle`: Aguardando
- `running`: Em execução
- `error`: Erro
- `success`: Sucesso

### **2. Job Manager**
**Localização**: `src/services/job-manager.ts`

**Responsabilidades**:
- Orquestração de jobs de captura de tokens
- Execução de coleta de dados
- Gerenciamento de configurações de providers
- Interface entre frontend e serviços externos

**Métodos Principais**:
- `executeTokenCaptureManually()`: Captura manual de tokens
- `executeDataCollectionManually()`: Coleta manual de dados
- `getSystemStatus()`: Status geral do sistema

### **3. Token Collector**
**Localização**: `src/services/token-capture/token-collector.ts`

**Funcionalidades**:
- Comunicação com API externa de captura de tokens
- Sistema de polling para monitoramento de jobs assíncronos
- Persistência de resultados no Firestore
- Tratamento de timeouts e erros

**Fluxo de Captura**:
1. Inicia job via `POST /api/capture-token`
2. Monitora progresso via `GET /api/capture-status/{jobId}`
3. Salva resultado no Firestore
4. Retorna status para interface

### **4. Data Collectors**
**Localização**: `src/services/data-collection/`

**Implementações**:
- `BaseDataCollector`: Classe abstrata base
- `GenericDataCollector`: Implementação genérica
- `GoodweDataCollector`: Implementação específica para Goodwe

## 🗄️ Estrutura de Dados

### **Firestore Collections**

#### **providersDataAccess**
```typescript
{
  id: string;
  provider_id: string;
  provider_slug: string;
  provider_access: string;
  panel_url: string;
  panel_user: string;
  panel_password: string;
  enabled: boolean;
}
```

#### **token_capture_results**
```typescript
{
  id: string;
  jobId: string;
  providerId: string;
  providerDataAccessId: string;
  panelName: string;
  token: string;
  cookies: string[];
  headers: Record<string, string>;
  timestamp: Date;
  success: boolean;
  error?: string;
  expiresAt?: Date;
}
```

#### **api_data_jobs**
```typescript
{
  id: string;
  campusId: string;
  usinaId: string;
  providerId: string;
  status: "pending" | "running" | "completed" | "failed";
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  dataCollected: number;
  logs: string[];
}
```

### **Interfaces TypeScript**
**Localização**: `src/types/services.ts`

**Principais Interfaces**:
- `ProviderTokenConfig`: Configuração de provider
- `TokenCaptureResult`: Resultado de captura de token
- `APIDataJob`: Job de coleta de dados
- `SystemStatus`: Status geral do sistema

## 🔄 Fluxos de Funcionamento

### **Captura de Token**
1. Usuário clica em "Capturar" no ProviderStatus
2. Frontend chama `executeTokenCaptureManually()`
3. Job Manager busca configuração do provider
4. Token Collector inicia job na API externa
5. Sistema monitora progresso via polling
6. Resultado é salvo no Firestore
7. Interface é atualizada automaticamente

### **Coleta de Dados**
1. Usuário clica em "Coletar" (apenas se token válido)
2. Frontend chama `executeDataCollectionManually()`
3. Job Manager busca usinas do provider
4. Data Collector executa coleta via API
5. Dados são salvos no Firestore
6. Interface é atualizada

## ⚙️ Configurações

### **Variáveis de Ambiente**
```env
VITE_APP_TOKEN_SCRAPER_URL=http://localhost:3002
```

### **Configurações de Polling**
- **Intervalo**: 5 segundos
- **Timeout**: 5 minutos (60 tentativas)
- **Estados monitorados**: `pending`, `running`, `completed`, `failed`

## 🎨 Interface Visual

### **Indicadores de Status**
- **Tokens**: Badges coloridos (verde=válido, vermelho=expirado, cinza=ausente)
- **Providers**: Status com cores (azul=idle, amarelo=running, vermelho=error, verde=success)
- **Progresso**: Spinners animados durante execução
- **Datas**: Formato brasileiro com hora (DD/MM/AAAA HH:MM)

### **Botões Condicionais**
- **Capturar Token**: Aparece apenas se `missing` ou `expired`
- **Coletar Dados**: Aparece apenas se token `valid`
- **Estados de Execução**: Texto e ícones mudam durante execução

## 🐛 Debug e Logs

### **Logs Implementados**
- **Token Collector**: Logs de salvamento no Firestore
- **ProviderStatus**: Logs de busca e processamento de dados
- **Job Manager**: Logs de execução de jobs

### **Console Debug**
```javascript
// Logs de salvamento
🔍 Debug - Dados salvos no Firestore: {docId, providerDataAccessId, success, timestamp}

// Logs de busca
🔍 Debug - Provider {id}: {providerDataAccessId, recentResults, results}

// Logs de processamento
📊 ProviderData final para {id}: {providerAccessId, tokenStatus, lastTokenUpdate}
```

## 🚀 Funcionalidades Principais

### **Monitoramento em Tempo Real**
- Status atualizado automaticamente
- Indicadores visuais de progresso
- Contadores de jobs executados

### **Operações Manuais**
- Captura de tokens sob demanda
- Coleta de dados manual
- Tratamento de erros com feedback visual

### **Persistência de Dados**
- Firestore para configurações e resultados
- Histórico de capturas e coletas
- Metadados de jobs e timestamps

## 🔧 Manutenção

### **Arquivos Principais**
- `ProviderStatus.tsx`: Interface principal
- `job-manager.ts`: Orquestração de jobs
- `token-collector.ts`: Captura de tokens
- `firebase-storage.ts`: Persistência de dados
- `types/services.ts`: Definições de tipos

### **Estrutura de Pastas**
```
src/
├── pages/admin/components/
│   └── ProviderStatus.tsx
├── services/
│   ├── job-manager.ts
│   ├── firebase-storage.ts
│   ├── token-capture/
│   │   ├── token-collector.ts
│   │   └── base-token-collector.ts
│   └── data-collection/
│       ├── base-data-collector.ts
│       ├── generic-data-collector.ts
│       └── goodwe-data-collector.ts
└── types/
    └── services.ts
```

## 📝 Notas Técnicas

### **Compatibilidade**
- **Frontend**: React 18+, Vite, TypeScript
- **Backend**: Node.js, APIs REST
- **Database**: Firebase Firestore
- **Styling**: Tailwind CSS

### **Performance**
- Polling otimizado (5s interval)
- Cache de dados no frontend
- Operações assíncronas não-bloqueantes

### **Segurança**
- Tokens armazenados de forma segura
- Credenciais criptografadas
- Validação de dados de entrada

---

**Última atualização**: 31/08/2025  
**Versão**: 1.0.0  
**Status**: Produção

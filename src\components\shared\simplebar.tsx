import { cn } from "@/utils/merge-classes";
import { ReactNode } from "react";
import SimpleBarReact from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";

interface SimpleBarProps {
  children: ReactNode;
  className?: string;
}

export default function SimpleBar({ children, className }: SimpleBarProps) {
  return (
    <SimpleBarReact className={cn("h-full", className)}>
      {children}
    </SimpleBarReact>
  );
}

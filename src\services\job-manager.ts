import {
  APIDataJob,
  ProviderTokenConfig,
  SystemMetrics,
  TokenCaptureResult,
} from "@/types/services";
import { BaseDataCollector } from "./data-collection/base-data-collector";
import { GenericDataCollector } from "./data-collection/generic-data-collector";
import { GoodweDataCollector } from "./data-collection/goodwe-data-collector";
import { FirebaseStorageService } from "./firebase-storage";
import { BaseTokenCollector } from "./token-capture/base-token-collector";
import { TokenCollector } from "./token-capture/token-collector";

export class ScrapingJobManager {
  protected storage: FirebaseStorageService;
  private isRunning: boolean = false;
  private tokenIntervalId?: NodeJS.Timeout;
  private dataIntervalId?: NodeJS.Timeout;

  constructor() {
    this.storage = new FirebaseStorageService();
  }

  // Inicia o agendador de jobs
  async startScheduler(): Promise<void> {
    if (this.isRunning) {
      console.log("Agendador já está rodando");
      return;
    }

    console.log("Iniciando novo agendador de jobs de scraping");
    this.isRunning = true;

    // Executa captura de tokens imediatamente
    await this.executeTokenCaptureJobs();

    // Agenda captura de tokens diária (às 6h da manhã)
    this.scheduleTokenCapture();

    // Agenda coleta de dados horária
    this.scheduleDataCollection();

    console.log("Agendador iniciado com sucesso");
  }

  // Para o agendador
  stopScheduler(): void {
    if (this.tokenIntervalId) {
      clearInterval(this.tokenIntervalId);
      this.tokenIntervalId = undefined;
    }
    if (this.dataIntervalId) {
      clearInterval(this.dataIntervalId);
      this.dataIntervalId = undefined;
    }
    this.isRunning = false;
    console.log("Agendador de jobs parado");
  }

  // Agenda captura de tokens diária
  private scheduleTokenCapture(): void {
    const now = new Date();
    const nextRun = new Date(now);
    nextRun.setHours(6, 0, 0, 0); // 6h da manhã

    if (nextRun <= now) {
      nextRun.setDate(nextRun.getDate() + 1); // Próximo dia
    }

    const timeUntilNextRun = nextRun.getTime() - now.getTime();

    // Executa imediatamente se for a primeira vez
    setTimeout(async () => {
      await this.executeTokenCaptureJobs();
      this.scheduleTokenCapture(); // Agenda próxima execução
    }, timeUntilNextRun);
  }

  // Agenda coleta de dados horária
  private scheduleDataCollection(): void {
    this.dataIntervalId = setInterval(
      async () => {
        await this.executeDataCollectionJobs();
      },
      60 * 60 * 1000
    ); // 1 hora
  }

  // Executa jobs de captura de tokens
  private async executeTokenCaptureJobs(): Promise<void> {
    try {
      console.log("Executando captura de tokens...");

      // Aqui você implementaria a lógica para buscar configurações de providers
      // Por enquanto, vamos usar um exemplo
      const providerConfigs = await this.getProviderTokenConfigs();

      for (const config of providerConfigs) {
        await this.executeTokenCaptureJob(config);
      }

      console.log("Captura de tokens concluída");
    } catch (error) {
      console.error("Erro na captura de tokens:", error);
    }
  }

  // Executa job de captura de token para um provider
  private async executeTokenCaptureJob(
    config: ProviderTokenConfig
  ): Promise<void> {
    try {
      console.log(
        `Executando captura de token para ${config.providerSlug.toUpperCase()} - Acesso: ${config.providerAccess[0].panelName}`
      );

      // Cria e executa o coletor de token
      const tokenCollector = this.createTokenScraper(config);

      // Para captura manual, usa captureAllTokens que processa todos os acessos
      const results = await tokenCollector.captureAllTokens();

      console.log(
        `Captura de token para acesso ${config.providerAccess[0].panelName} concluída com sucesso`
      );
      console.log(
        `Resultados: ${results.filter((r: TokenCaptureResult) => r.success).length}/${results.length} tokens capturados`
      );
    } catch (error) {
      console.error(
        `Erro na captura de token para ${config.providerSlug.toUpperCase()} - Acesso ${config.providerSlug}:`,
        error
      );
      throw error;
    }
  }

  // Executa jobs de coleta de dados via API
  private async executeDataCollectionJobs(): Promise<void> {
    try {
      console.log("Executando coleta de dados via API...");

      // Aqui você implementaria a lógica para buscar campus e usinas
      // Por enquanto, vamos usar um exemplo
      const dataJobs = await this.getDataCollectionJobs();

      for (const job of dataJobs) {
        await this.executeDataCollectionJob(job);
      }

      console.log("Coleta de dados via API concluída");
    } catch (error) {
      console.error("Erro na coleta de dados via API:", error);
    }
  }

  // Executa job de coleta de dados para uma usina
  protected async executeDataCollectionJob(job: APIDataJob): Promise<void> {
    try {
      console.log(`Executando coleta de dados para usina ${job.usinaId}`);

      // Busca informações da usina e provider
      const { doc, getDoc } = await import("firebase/firestore");
      const { db } = await import("@/lib/firebase");

      const usinaDoc = await getDoc(doc(db, "usinas", job.usinaId));
      if (!usinaDoc.exists()) {
        throw new Error(`Usina ${job.usinaId} não encontrada`);
      }

      const usinaData = usinaDoc.data();
      const providerUsinaId = usinaData.provider_usina_id;
      if (!providerUsinaId) {
        throw new Error(`provider_usina_id não encontrado para usina ${job.usinaId}`);
      }

      // Busca informações do provider
      const providerDoc = await getDoc(doc(db, "providers", job.providerId));
      if (!providerDoc.exists()) {
        throw new Error(`Provider ${job.providerId} não encontrado`);
      }
      
      const providerData = providerDoc.data();
      const providerSlug = providerData.provider_slug;
      const apiUrl = providerData.api_url;

      // Busca o token do provider
      const apiToken = await this.getProviderAPIToken(job.providerId);
      if (!apiToken) {
        throw new Error("Token do provider não encontrado");
      }

      // Cria e executa o coletor de dados
      const collector = this.createDataCollector(providerSlug, apiToken, apiUrl);
      const results = await collector.collectUsinaData(
        job.usinaId,
        providerUsinaId // Usa o ID correto da usina no provider
      );

      // Salva os resultados no Firestore
      if (results.length > 0) {
        await this.storage.saveAPICallResults(results);
        console.log(`✅ ${results.length} resultados salvos no Firestore`);
      }

      console.log(
        `Coleta de dados para usina ${job.usinaId} concluída com sucesso`
      );
      console.log(`Resultados: ${results.length} endpoints coletados`);
    } catch (error) {
      console.error(
        `Erro na coleta de dados para usina ${job.usinaId}:`,
        error
      );
      throw error;
    }
  }

  // Métodos auxiliares implementados
  protected async getProviderTokenConfigs(): Promise<ProviderTokenConfig[]> {
    try {
      // Buscar configurações de todos os providers do Firestore
      const { collection, getDocs } = await import("firebase/firestore");
      const { db } = await import("@/lib/firebase");

      // Buscar na coleção providersDataAccess
      const providersDataAccessSnapshot = await getDocs(
        collection(db, "providersDataAccess")
      );
      const providers: ProviderTokenConfig[] = [];

      for (const doc of providersDataAccessSnapshot.docs) {
        const data = doc.data();

        // Converter dados do Firestore para ProviderTokenConfig
        const config: ProviderTokenConfig = {
          id: doc.id,
          providerId: data.provider_id || data.providerId || doc.id,
          providerSlug: data.provider_slug || "default",
          baseUrl: data.panel_url || data.panelUrl || "",
          providerAccess: [
            {
              id: `${doc.id}_panel`,
              panelName:
                data.provider_access || data.providerAccess || "default",
              panelUrl: data.panel_url || data.panelUrl || "",
              credentials: {
                username: data.panel_user || data.panelUser || "",
                password: data.panel_password || data.panelPassword || "",
              },
              endpoints: {
                login: "/api/login", // Endpoint padrão
              },
              headers: {},
            },
          ],
          enabled: true, // Sempre habilitado
          status: "idle",
          lastTokenUpdate: undefined,
          nextTokenUpdate: undefined,
        };

        providers.push(config);
      }

      return providers;
    } catch (error) {
      console.error("Erro ao buscar configurações de providers:", error);
      return [];
    }
  }

  protected async getDataCollectionJobs(): Promise<APIDataJob[]> {
    try {
      // Buscar usinas do Firestore e criar jobs
      const { collection, getDocs } = await import("firebase/firestore");
      const { db } = await import("@/lib/firebase");

      const usinasSnapshot = await getDocs(collection(db, "usinas"));
      const jobs: APIDataJob[] = [];

      for (const doc of usinasSnapshot.docs) {
        const usina = doc.data();

        if (usina.providerId && usina.campusId) {
          const job: APIDataJob = {
            id: `data_job_${doc.id}_${Date.now()}`,
            campusId: usina.campusId,
            usinaId: doc.id,
            providerId: usina.providerId,
            status: "pending",
            dataCollected: 0,
            logs: [`Job criado para usina ${usina.name || doc.id}`],
          };
          jobs.push(job);
        }
      }

      return jobs;
    } catch (error) {
      console.error("Erro ao buscar jobs de coleta de dados:", error);
      return [];
    }
  }

  protected async getProviderAPIToken(
    providerId: string
  ): Promise<string | null> {
    try {
      // Buscar token mais recente do provider diretamente do Firestore
      const { collection, query, where, orderBy, limit, getDocs } =
        await import("firebase/firestore");
      const { db } = await import("@/lib/firebase");

      // Buscar na coleção token_capture_results
      const tokenQuery = query(
        collection(db, "token_capture_results"),
        where("providerId", "==", providerId),
        where("success", "==", true),
        orderBy("timestamp", "desc"),
        limit(1)
      );

      const tokenSnapshot = await getDocs(tokenQuery);

      if (tokenSnapshot.empty) {
        return null;
      }

      const tokenDoc = tokenSnapshot.docs[0];
      const tokenData = tokenDoc.data();

      return tokenData.token || null;
    } catch (error) {
      console.error(`Erro ao buscar token do provider ${providerId}:`, error);
      return null;
    }
  }

  protected createTokenScraper(
    config: ProviderTokenConfig
  ): BaseTokenCollector {
    // Criar coletor de token genérico baseado na configuração
    return new TokenCollector(config);
  }

  protected createDataCollector(
    providerSlug: string,
    apiToken: string,
    apiUrl: string
  ): BaseDataCollector {
    // Criar coletor de dados específico baseado no provider
    if (providerSlug.toLowerCase().includes("goodwe")) {
      return new GoodweDataCollector(providerSlug, apiToken, apiUrl);
    }

    // Para outros providers, usar o coletor genérico
    return new GenericDataCollector(providerSlug, apiToken, apiUrl);
  }

  // Executa captura de tokens manualmente
  async executeTokenCaptureManually(providerAccessId: string): Promise<void> {
    try {
      // Buscar configuração diretamente do Firestore
      const { doc, getDoc } = await import("firebase/firestore");
      const { db } = await import("@/lib/firebase");

      const providerDataAccessDoc = await getDoc(
        doc(db, "providersDataAccess", providerAccessId)
      );

      if (!providerDataAccessDoc.exists()) {
        throw new Error(
          `Acesso de provider não encontrado: ${providerAccessId}`
        );
      }

      const data = providerDataAccessDoc.data();

      // Converter para ProviderTokenConfig
      const config: ProviderTokenConfig = {
        id: providerDataAccessDoc.id,
        providerId:
          data.provider_id || data.providerId || providerDataAccessDoc.id,
        providerSlug: data.provider_slug || "default",
        baseUrl: data.panel_url || data.panelUrl || "",
        providerAccess: [
          {
            id: `${providerDataAccessDoc.id}_panel`,
            panelName: data.provider_access || data.providerAccess || "default",
            panelUrl: data.panel_url || data.panelUrl || "",
            credentials: {
              username: data.panel_user || data.panelUser || "",
              password: data.panel_password || data.panelPassword || "",
            },
            endpoints: {
              login: "/api/login",
            },
            headers: {},
          },
        ],
        enabled: true,
        status: "idle",
        lastTokenUpdate: undefined,
        nextTokenUpdate: undefined,
      };

      // Executa captura de token para este acesso específico
      await this.executeTokenCaptureJob(config);
    } catch (error) {
      console.error(`Erro ao buscar configuração de provider: ${error}`);
      throw new Error(`Erro ao buscar configuração de provider: ${error}`);
    }
  }

  // Executa coleta de dados manualmente
  async executeDataCollectionManually(usinaId: string): Promise<void> {
    try {
      // Buscar usina diretamente do Firestore
      const { doc, getDoc } = await import("firebase/firestore");
      const { db } = await import("@/lib/firebase");

      const usinaDoc = await getDoc(doc(db, "usinas", usinaId));

      if (!usinaDoc.exists()) {
        throw new Error(`Usina não encontrada: ${usinaId}`);
      }

      const usinaData = usinaDoc.data();

      if (!usinaData.providerId || !usinaData.campusId) {
        throw new Error(
          `Usina ${usinaId} não tem providerId ou campusId configurados`
        );
      }

      // Criar job de coleta de dados no Firestore
      const job: APIDataJob = {
        id: `data_job_${usinaId}_${Date.now()}`,
        campusId: usinaData.campusId,
        usinaId: usinaId,
        providerId: usinaData.providerId,
        status: "pending",
        startedAt: new Date(),
        dataCollected: 0,
        logs: [`Job criado para usina ${usinaData.name || usinaId}`],
      };

      // Salvar job no Firestore primeiro
      const jobId = await this.storage.createAPIDataJob(job);
      console.log(`📝 Job criado no Firestore: ${jobId}`);

      // Atualizar status para running
      await this.storage.updateAPIDataJob(jobId, {
        status: "running",
        logs: [...job.logs, "Iniciando coleta de dados"]
      });

      try {
        // Executar coleta
        await this.executeDataCollectionJob({ ...job, id: jobId });
        
        // Marcar como concluído
        await this.storage.updateAPIDataJob(jobId, {
          status: "completed",
          completedAt: new Date(),
          logs: [...job.logs, "Coleta concluída com sucesso"]
        });
      } catch (error) {
        // Marcar como falhou
        await this.storage.updateAPIDataJob(jobId, {
          status: "failed",
          completedAt: new Date(),
          error: error instanceof Error ? error.message : String(error),
          logs: [...job.logs, `Erro: ${error}`]
        });
        throw error;
      }
    } catch (error) {
      console.error(`Erro ao buscar usina: ${error}`);
      throw new Error(`Erro ao buscar usina: ${error}`);
    }
  }

  // Obtém status do sistema
  async getSystemStatus(): Promise<{
    tokenJobs: any[];
    dataJobs: any[];
    metrics: SystemMetrics | null;
  }> {
    try {
      // Buscar jobs diretamente do Firestore
      const { collection, query, orderBy, limit, getDocs } = await import(
        "firebase/firestore"
      );
      const { db } = await import("@/lib/firebase");

      // Buscar jobs de token recentes
      const tokenJobsQuery = query(
        collection(db, "provider_token_jobs"),
        orderBy("createdAt", "desc"),
        limit(10)
      );
      const tokenJobsSnapshot = await getDocs(tokenJobsQuery);
      const tokenJobs = tokenJobsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Buscar jobs de dados recentes
      const dataJobsQuery = query(
        collection(db, "api_data_jobs"),
        orderBy("createdAt", "desc"),
        limit(10)
      );
      const dataJobsSnapshot = await getDocs(dataJobsQuery);
      const dataJobs = dataJobsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      return {
        tokenJobs,
        dataJobs,
        metrics: null, // Por enquanto retorna null para metrics
      };
    } catch (error) {
      console.error("Erro ao buscar status do sistema:", error);
      return { tokenJobs: [], dataJobs: [], metrics: null };
    }
  }
}

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

const data = [{ name: "Máxima do Dia", value: 400 }];

const COLORS = ["#FFBB28"];

// Função para desenhar o ponteiro (needle)
function renderNeedle(
  value: number,
  total: number,
  cx: number,
  cy: number,
  radius: number
) {
  const angle = 180 * (value / total);
  const rad = Math.PI * (angle / 180);
  const x = cx + radius * Math.cos(Math.PI - rad);
  const y = cy - radius * Math.sin(Math.PI - rad);

  return (
    <g>
      <line x1={cx} y1={cy} x2={x} y2={y} stroke="#d00" strokeWidth={4} />
      <circle cx={cx} cy={cy} r={5} fill="#d00" />
    </g>
  );
}

export default function PieChartWithNeedle({
  value,
  total,
  className,
}: {
  value: number;
  total: number;
  className?: string;
}) {
  const cx = 120;
  const cy = 120;
  const radius = 100;

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart height={120}>
          <Pie
            data={data}
            cx={cx}
            cy={cy}
            startAngle={180}
            endAngle={0}
            innerRadius={60}
            outerRadius={radius}
            fill="#8884d8"
            paddingAngle={2}
            dataKey="value"
            cornerRadius={8}
          >
            {data.map((_, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
          {renderNeedle(value, total, cx, cy, radius)}
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

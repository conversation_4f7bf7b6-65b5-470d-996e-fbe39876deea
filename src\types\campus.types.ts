import type { Provider } from "./provider.types";

// Tipos base independentes (sem Amplify)
export interface Campus {
  id: string;
  name: string;
  city: string;
  lat: number;
  long: number;
  status: "ACTIVE" | "INACTIVE";
  maintenanceMode?: boolean; // modo manutenção independente do status
  createdAt: string;
  updatedAt: string;
}

export interface Usina {
  id: string;
  name: string;
  capacity: number;
  lat: number;
  long: number;
  model: string;
  module_qtd: number;
  eficiency: number;
  area_m2: number;
  azimut: number;
  provider_usina_id: string;
  campusId: string;
  providerId: string;
  provider_access?: string; // dados de acesso no provider (ProviderDataAccess)
  status: "ACTIVE" | "INACTIVE";
  createdAt: string;
  updatedAt: string;
}

// Tipos para formulários de Campus
export interface CreateCampusData {
  name: string;
  city: string;
  lat: number;
  long: number;
  status: "ACTIVE" | "INACTIVE";
  maintenanceMode?: boolean;
}

export interface UpdateCampusData {
  name: string;
  city: string;
  lat: number;
  long: number;
  status: "ACTIVE" | "INACTIVE";
  maintenanceMode?: boolean;
}

// Tipos para formulários de Usina
export interface CreateUsinaData {
  name: string;
  capacity: number;
  lat: number;
  long: number;
  model: string;
  module_qtd: number;
  eficiency: number;
  area_m2: number;
  azimut: number;
  provider_usina_id: string;
  campusId: string;
  providerId: string;
  provider_access?: string;
  status: "ACTIVE" | "INACTIVE";
}

export interface UpdateUsinaData {
  name: string;
  capacity: number;
  lat: number;
  long: number;
  model: string;
  module_qtd: number;
  eficiency: number;
  area_m2: number;
  azimut: number;
  provider_usina_id: string;
  campusId: string;
  providerId: string;
  provider_access?: string;
  status: "ACTIVE" | "INACTIVE";
}

// Tipos para validação
export interface CampusFormErrors {
  name?: string;
  city?: string;
  lat?: string;
  long?: string;
  status?: string;
}

export interface UsinaFormErrors {
  name?: string;
  capacity?: string;
  lat?: string;
  long?: string;
  model?: string;
  module_qtd?: string;
  eficiency?: string;
  area_m2?: string;
  azimut?: string;
  provider_usina_id?: string;
  campusId?: string;
  providerId?: string;
  status?: string;
}

// Tipos para listagem com informações adicionais
export interface CampusListItem extends Campus {
  usinasCount?: number;
  totalCapacity?: number;
  activeUsinas?: number;
}

export interface UsinaListItem extends Usina {
  campusName?: string;
  providerName?: string;
}

// Tipos para relacionamentos (sem extends para evitar conflitos)
export interface CampusWithUsinas {
  id: string;
  name: string;
  city: string;
  lat: number;
  long: number;
  status: "ACTIVE" | "INACTIVE" | "MAINTENANCE";
  usinas?: Usina[];
  createdAt: string;
  updatedAt: string;
}

export interface UsinaWithRelations {
  id: string;
  name: string;
  capacity: number;
  lat: number;
  long: number;
  model: string;
  module_qtd: number;
  eficiency: number;
  area_m2: number;
  azimut: number;
  provider_usina_id: string;
  campusId: string;
  providerId: string;
  campus?: Campus;
  provider?: Provider;
  createdAt: string;
  updatedAt: string;
}

// Opções de status para Campus
export const CAMPUS_STATUS_OPTIONS = [
  { value: "ACTIVE", label: "Ativo" },
  { value: "INACTIVE", label: "Inativo" },
] as const;

// Tipo para opções de select
export interface SelectOption {
  value: string;
  label: string;
}

// Tipo para subscription do Amplify observeQuery
export interface AmplifySubscription {
  unsubscribe: () => void;
}

// Tipos para valores de input em formulários
export type FormInputValue = string | number | boolean;

// Union types específicos para campos de formulário
export type CampusFormValue = string | number;
export type UsinaFormValue = string | number;

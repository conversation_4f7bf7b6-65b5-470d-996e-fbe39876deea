/**
 * Utilitários para auxiliar no trabalho com formulários
 */

import { isRequired, isValidUrl, isValidLatitude, isValidLongitude, isValidCapacity, isValidEfficiency, isValidAzimuth, isValidArea, isValidModelQuantity } from './validation';

// Tipos para erros de formulário
export type FormErrors<T> = Partial<Record<keyof T, string>>;

// Função genérica para validar campos obrigatórios
export const validateRequiredFields = <T extends Record<string, any>>(
  data: T,
  requiredFields: (keyof T)[]
): FormErrors<T> => {
  const errors: FormErrors<T> = {};
  
  requiredFields.forEach(field => {
    if (!isRequired(data[field])) {
      errors[field] = 'Este campo é obrigatório';
    }
  });
  
  return errors;
};

// Função para validar URL
export const validateUrl = (url: string): string | undefined => {
  if (!isRequired(url)) {
    return 'URL é obrigatória';
  }
  if (!isValidUrl(url)) {
    return 'URL inválida';
  }
  return undefined;
};

// Função para validar coordenadas
export const validateCoordinates = (lat: number, lng: number): { lat?: string; lng?: string } => {
  const errors: { lat?: string; lng?: string } = {};
  
  if (!isValidLatitude(lat)) {
    errors.lat = 'Latitude deve estar entre -90 e 90';
  }
  
  if (!isValidLongitude(lng)) {
    errors.lng = 'Longitude deve estar entre -180 e 180';
  }
  
  return errors;
};

// Função para validar capacidade
export const validateCapacity = (capacity: number): string | undefined => {
  if (!isValidCapacity(capacity)) {
    return 'Capacidade deve ser um número positivo';
  }
  return undefined;
};

// Função para validar eficiência
export const validateEfficiency = (efficiency: number): string | undefined => {
  if (!isValidEfficiency(efficiency)) {
    return 'Eficiência deve estar entre 0 e 100%';
  }
  return undefined;
};

// Função para validar azimute
export const validateAzimuth = (azimuth: number): string | undefined => {
  if (!isValidAzimuth(azimuth)) {
    return 'Azimute deve estar entre 0 e 360 graus';
  }
  return undefined;
};

// Função para validar área
export const validateArea = (area: number): string | undefined => {
  if (!isValidArea(area)) {
    return 'Área deve ser um número positivo';
  }
  return undefined;
};

// Função para validar quantidade de modelos
export const validateModelQuantity = (quantity: number): string | undefined => {
  if (!isValidModelQuantity(quantity)) {
    return 'Quantidade deve ser um número inteiro positivo';
  }
  return undefined;
};

// Função para limpar erros de um campo específico
export const clearFieldError = <T>(
  errors: FormErrors<T>,
  field: keyof T,
  setErrors: (errors: FormErrors<T>) => void
): void => {
  if (errors[field]) {
    const newErrors = { ...errors };
    delete newErrors[field];
    setErrors(newErrors);
  }
};

// Função para atualizar dados do formulário
export const updateFormData = <T>(
  currentData: T,
  field: keyof T,
  value: any,
  setData: (data: T) => void
): void => {
  setData({
    ...currentData,
    [field]: value,
  });
};

// Função para resetar formulário
export const resetForm = <T>(
  initialData: T,
  setData: (data: T) => void,
  setErrors: (errors: FormErrors<T>) => void
): void => {
  setData(initialData);
  setErrors({});
};

// Função para converter string para número com fallback
export const parseNumber = (value: string, fallback: number = 0): number => {
  const parsed = parseFloat(value);
  return isNaN(parsed) ? fallback : parsed;
};

// Função para converter string para inteiro com fallback
export const parseInteger = (value: string, fallback: number = 0): number => {
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
};

// Função para formatar input de número
export const formatNumberInput = (value: number, decimals: number = 2): string => {
  return value.toFixed(decimals);
};

// Função para validar se há erros no formulário
export const hasFormErrors = <T>(errors: FormErrors<T>): boolean => {
  return Object.keys(errors).length > 0;
};

// Função para obter mensagem de erro de um campo
export const getFieldError = <T>(errors: FormErrors<T>, field: keyof T): string | undefined => {
  return errors[field];
};

// Função para marcar todos os campos como tocados (para mostrar erros)
export const touchAllFields = <T extends Record<string, any>>(
  data: T,
  requiredFields: (keyof T)[],
  setErrors: (errors: FormErrors<T>) => void
): void => {
  const errors = validateRequiredFields(data, requiredFields);
  setErrors(errors);
};

import { Table } from "@/components/shared/table";
import { useTanStackTable } from "@/components/shared/table/custom/use-tan-stack-table";
import { TablePagination } from "@/components/shared/table/pagination";
import WidgetCard from "@/components/shared/widget-card";
import { But<PERSON> } from "@/components/ui/button";
import type { Campus } from "@/types";
import { formatCoordinate, formatStatus } from "@/utils";
import { ColumnDef } from "@tanstack/react-table";
import { Eye, Pencil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useCampus } from "../hooks/useCampus";

export default function CampusListPage() {
  const { campus, loading, error, deleteCampus } = useCampus();
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDelete = async (id: string) => {
    if (
      window.confirm(
        "Tem certeza que deseja excluir este campus? Todas as usinas vinculadas também serão removidas."
      )
    ) {
      setDeletingId(id);
      try {
        await deleteCampus(id);
      } catch (error) {
        console.error("Erro ao excluir campus:", error);
        alert("Erro ao excluir campus. Tente novamente.");
      } finally {
        setDeletingId(null);
      }
    }
  };

  const columns: ColumnDef<Campus>[] = [
    {
      accessorKey: "name",
      header: "Nome",
      cell: ({ getValue }) => (
        <span className="font-medium text-gray-900">
          {getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "city",
      header: "Cidade",
      cell: ({ getValue }) => (
        <span className="text-gray-700">{getValue() as string}</span>
      ),
    },
    {
      accessorKey: "lat",
      header: "Latitude",
      cell: ({ getValue }) => {
        const value = getValue();
        if (value === null || value === undefined) {
          return <span className="text-gray-400 font-mono text-sm">N/A</span>;
        }
        return (
          <span className="text-gray-600 font-mono text-sm">
            {formatCoordinate(value as number)}
          </span>
        );
      },
    },
    {
      accessorKey: "long",
      header: "Longitude",
      cell: ({ getValue }) => {
        const value = getValue();
        if (value === null || value === undefined) {
          return <span className="text-gray-400 font-mono text-sm">N/A</span>;
        }
        return (
          <span className="text-gray-600 font-mono text-sm">
            {formatCoordinate(value as number)}
          </span>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ getValue }) => {
        const status = getValue() as string;
        const statusColors = {
          ACTIVE: "bg-green-100 text-green-800",
          INACTIVE: "bg-red-100 text-red-800",
          MAINTENANCE: "bg-yellow-100 text-yellow-800",
        };

        return (
          <span
            className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}`}
          >
            {formatStatus(status)}
          </span>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Criado em",
      cell: ({ getValue }) => {
        const date = new Date(getValue() as string);
        return (
          <span className="text-gray-600">
            {date.toLocaleDateString("pt-BR")}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Ações",
      cell: ({ row }) => {
        const campusItem = row.original;
        const isDeleting = deletingId === campusItem.id;

        return (
          <div className="flex items-center gap-2">
            <Link to={`/admin/campus/detail/${campusItem.id}`}>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Eye className="h-4 w-4" />
              </Button>
            </Link>
            <Link to={`/admin/campus/edit/${campusItem.id}`}>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Pencil className="h-4 w-4" />
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
              onClick={() => handleDelete(campusItem.id)}
              disabled={isDeleting}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const { table } = useTanStackTable({
    tableData: campus || [],
    columnConfig: columns,
    options: {
      initialState: {
        pagination: {
          pageIndex: 0,
          pageSize: 10,
        },
      },
    },
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Carregando campus...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Erro ao carregar campus: {error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Campus</h1>
          <p className="text-gray-600">
            Gerencie os campus e suas usinas solares
          </p>
        </div>
        <Link to="/admin/campus/create">
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Novo Campus
          </Button>
        </Link>
      </div>

      <WidgetCard>
        <Table data={campus || []} columns={columns} />
        <TablePagination table={table} />
      </WidgetCard>
    </div>
  );
}

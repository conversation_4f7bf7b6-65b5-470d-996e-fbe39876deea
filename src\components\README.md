# Organização de Componentes - MonitoraLux

Este documento descreve a arquitetura e organização dos componentes no projeto MonitoraLux.

## Estrutura de Pastas

```
src/components/
├── layout/          # Componentes de estrutura geral da aplicação
├── shared/          # Componentes reutilizáveis específicos do domínio
├── ui/              # Componentes base do design system (Shadcn/ui)
```

## 📁 Layout (`/layout`)

Componentes responsáveis pela estrutura geral da aplicação:

- **Header.tsx** - Cabeçalho principal da aplicação
- **Sidebar.tsx** - Menu lateral de navegação
- **Layout.tsx** - Layout principal que combina header e sidebar
- **Logo.tsx** - Componente do logotipo
- **HamburgerButton.tsx** - Botão para toggle do menu mobile

### Quando usar:

- Componentes que definem a estrutura base da aplicação
- Elementos de navegação global
- Layouts de página

## 📁 Shared (`/shared`)

Componentes reutilizáveis específicos do domínio da aplicação:

### Componentes de Dados:

- **CampusSelect.tsx** - Seletor de campus
- **ProviderSelect.tsx** - Seletor de provedores
- **card-\*.tsx** - Cards para exibição de dados específicos

### Componentes de Interface:

- **date-navigator.tsx** - Navegador de datas
- **dropdown-action.tsx** - Dropdown para ações
- **custom-tooltip.tsx** - Tooltip customizado
- **widget-card.tsx** - Card genérico para widgets

### Tabelas:

- **table/** - Sistema de tabelas avançado com TanStack Table
  - `index.tsx` - Componente principal da tabela
  - `pagination.tsx` - Componente de paginação
  - `custom/use-tan-stack-table.ts` - Hook customizado

### Quando usar:

- Componentes que encapsulam lógica de negócio
- Componentes reutilizáveis em múltiplas páginas
- Integrações com APIs ou estado global

## 📁 UI (`/ui`)

Componentes base do design system (baseados no Shadcn/ui):

### Componentes Básicos:

- **button.tsx** - Botões em diferentes variantes
- **input.tsx** - Campos de entrada
- **label.tsx** - Labels para formulários
- **card.tsx** - Cards básicos

### Componentes de Formulário:

- **form.tsx** - Sistema de formulários com React Hook Form
- **select.tsx** - Componente de seleção
- **radio-group.tsx** - Grupo de radio buttons
- **checkbox.tsx** - Checkboxes
- **switch.tsx** - Switches/toggles

### Componentes de Interface:

- **dialog.tsx** - Modais e diálogos
- **dropdown-menu.tsx** - Menus dropdown
- **tabs.tsx** - Sistema de abas
- **badge.tsx** - Badges e tags
- **alert.tsx** - Alertas e notificações

### Componentes de Layout:

- **table.tsx** - Tabela básica (para dados simples)

### Quando usar:

- Componentes puramente visuais sem lógica de negócio
- Elementos base do design system
- Componentes que seguem padrões de acessibilidade

## 🔧 Melhorias Implementadas

### 1. Remoção do "use client"

- Removido `"use client"` dos componentes UI (específico do Next.js)
- Componentes agora funcionam corretamente com Vite

### 2. TanStack Table Melhorado

- Integração com estilos do Shadcn para consistência visual
- Suporte a sorting, filtering e paginação
- Melhor acessibilidade e responsividade
- Estado de "nenhum resultado encontrado"

### 3. Paginação Modernizada

- Novo design baseado no Shadcn
- Ícones do Lucide React para consistência
- Informações de resultados mais claras

## 📋 Boas Práticas

### Nomenclatura:

- **PascalCase** para componentes React
- **kebab-case** para arquivos utilitários
- **camelCase** para hooks e funções

### Importações:

- Use `@/utils/merge-classes` para a função `cn` (utilitário de merge de classes)
- Use APENAS ícones do Lucide React (migração completa do React Icons)
- Importe tipos explicitamente quando necessário

### Estrutura de Componentes:

```typescript
// 1. Imports externos
import * as React from "react";
import { type VariantProps } from "class-variance-authority";

// 2. Imports internos
import { cn } from "@/utils/merge-classes";

// 3. Types e interfaces
interface ComponentProps {
  // ...
}

// 4. Componente
export const Component = ({ ...props }: ComponentProps) => {
  // ...
};
```

## 🚀 Próximos Passos

1. **Testes**: Implementar testes unitários para componentes críticos
2. **Performance**: Otimizações com React.memo onde necessário

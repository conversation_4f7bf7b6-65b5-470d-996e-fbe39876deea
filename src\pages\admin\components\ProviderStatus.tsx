import WidgetCard from "@/components/shared/widget-card";
import { Button } from "@/components/ui/button";
import { db } from '@/lib/firebase';
import { FirebaseStorageService, ScrapingJobManager } from "@/services";
import { collection, getDocs } from 'firebase/firestore';
import { AlertTriangle, BarChart3, Database, Download, Key, RefreshCw, Zap } from "lucide-react";
import { useEffect, useState } from "react";

interface ProviderStatusData {
  providerId: string;
  providerName: string;
  providerAccessId: string; // ID do ProviderDataAccess
  accessName: string; // Nome do acesso (ex: default, dashboard)
  tokenStatus: 'valid' | 'expired' | 'missing';
  lastTokenUpdate?: Date;
  totalUsinas: number;
  usinasComDados: number;
  lastDataCollection?: Date;
  status: 'idle' | 'running' | 'error' | 'success';
}

interface Usina {
  id: string;
  name: string;
  providerId: string;
  campusId: string;
  lastDataCollection?: Date;
}

interface Provider {
  id: string;
  name: string;
}

interface SystemStatus {
  providersAccess: ProviderStatusData[];
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  lastUpdate: Date;
}

export default function ProviderStatus() {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [executingToken, setExecutingToken] = useState<string | null>(null);
  const [executingData, setExecutingData] = useState<string | null>(null);
  const [tokenError, setTokenError] = useState<string | null>(null);
  const [dataError, setDataError] = useState<string | null>(null);
  const [cancelDataCollection, setCancelDataCollection] = useState(false);
  const [collectionProgress, setCollectionProgress] = useState<{ current: number; total: number } | null>(null);

  const fetchStatus = async () => {
    setRefreshing(true);
    try {
      const storage = new FirebaseStorageService();
      const jobManager = new ScrapingJobManager();

      // Obter status do sistema
      let systemStatus;
      try {
        systemStatus = await jobManager.getSystemStatus();
      } catch (error) {
        console.error('Erro ao obter system status:', error);
        // Status padrão em caso de erro
        systemStatus = {
          tokenJobs: [],
          dataJobs: [],
          metrics: null
        };
      }

      // Buscar dados do banco
      const [providersDataAccessSnapshot, usinasSnapshot, providersSnapshot] = await Promise.all([
        getDocs(collection(db, 'providersDataAccess')),
        getDocs(collection(db, 'usinas')),
        getDocs(collection(db, 'providers'))
      ]);

      const providerDataAccess: any[] = providersDataAccessSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const usinas: Usina[] = usinasSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Usina));

      const providers: Provider[] = providersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Provider));

      // Construir status dos providers
      const providersAccess: ProviderStatusData[] = [];

      for (const config of providerDataAccess) {
        try {
          // Verificar status do token
          // Busca pelo ID do ProviderDataAccess (config.id)
          const recentResults = await storage.getRecentTokenCaptureResultsByDataAccess(config.id, 1);
          console.log(`🔍 Debug - Provider ${config.id}:`, {
            providerDataAccessId: config.id,
            recentResults: recentResults.length,
            results: recentResults
          });

          let tokenStatus: 'valid' | 'expired' | 'missing' = 'missing';
          let lastTokenUpdate: Date | undefined;

          if (recentResults.length > 0 && recentResults[0].success) {
            const lastResult = recentResults[0];
            lastTokenUpdate = lastResult.timestamp;

            if (lastResult.expiresAt && lastResult.expiresAt > new Date()) {
              tokenStatus = 'valid';
            } else {
              tokenStatus = 'expired';
            }
            console.log(`✅ Token encontrado para ${config.id}:`, {
              status: tokenStatus,
              lastUpdate: lastTokenUpdate,
              expiresAt: lastResult.expiresAt
            });
          } else {
            console.log(`❌ Nenhum token válido encontrado para ${config.id}`);
          }

          // Contar usinas deste provider (usando provider_id do config)
          const usinasDoProvider = usinas.filter(u => u.providerId === config.provider_id);
          const totalUsinas = usinasDoProvider.length;
          
          console.log(`🔍 Debug - Usinas do provider ${config.provider_id}:`, {
            configId: config.id,
            providerId: config.provider_id,
            totalUsinas,
            usinasIds: usinasDoProvider.map(u => u.id)
          });

          // Verificar última coleta de dados
          let lastDataCollection: Date | undefined;
          let usinasComDados = 0;

          if (usinasDoProvider.length > 0) {
            // Buscar última coleta de dados das usinas deste provider
            const dataJobs = await storage.getRecentAPIDataJobs(10);
            const jobsDoProvider = dataJobs.filter((job: any) =>
              usinasDoProvider.some(u => u.id === job.usinaId)
            );

            if (jobsDoProvider.length > 0) {
              const ultimoJob = jobsDoProvider[0];
              if (ultimoJob.completedAt) {
                lastDataCollection = ultimoJob.completedAt;
              }
            }

            // Contar usinas com dados recentes (últimas 24h)
            const vinteQuatroHorasAtras = new Date(Date.now() - 24 * 60 * 60 * 1000);
            usinasComDados = usinasDoProvider.filter(u => {
              const job = dataJobs.find((j: any) => j.usinaId === u.id);
              return job && job.completedAt && job.completedAt > vinteQuatroHorasAtras;
            }).length;
          }

          // Determinar status do provider
          let providerStatus: 'idle' | 'running' | 'error' | 'success' = 'idle';
          if (tokenStatus === 'valid' && usinasComDados > 0) {
            providerStatus = 'success';
          } else if (tokenStatus === 'expired' || tokenStatus === 'missing') {
            providerStatus = 'error';
          } else if (tokenStatus === 'valid' && usinasComDados === 0) {
            providerStatus = 'idle';
          }
          
          console.log(`📊 Status do provider ${config.id}:`, {
            tokenStatus,
            usinasComDados,
            totalUsinas,
            providerStatus
          });

          const providerData = {
            providerId: config.provider_id || config.providerId || config.id,
            providerName: providers.find(p => p.id === config.provider_id)?.name || 'Provider',
            providerAccessId: config.id,
            accessName: config.provider_access || config.providerAccess || 'default',
            tokenStatus,
            lastTokenUpdate,
            totalUsinas,
            usinasComDados,
            lastDataCollection,
            status: providerStatus
          };

          console.log(`📊 ProviderData final para ${config.id}:`, {
            providerAccessId: providerData.providerAccessId,
            tokenStatus: providerData.tokenStatus,
            lastTokenUpdate: providerData.lastTokenUpdate
          });

          providersAccess.push(providerData);
        } catch (error) {
          console.error(`Erro ao processar provider ${config.id}:`, error);
          // Adiciona provider com status básico
          providersAccess.push({
            providerId: config.provider_id || config.providerId || config.id,
            providerName: providers.find(p => p.id === config.provider_id)?.name || 'Provider',
            providerAccessId: config.id,
            accessName: config.provider_access || config.providerAccess || 'default',
            tokenStatus: 'missing' as const,
            totalUsinas: 0,
            usinasComDados: 0,
            status: 'error' as const
          });
        }
      }

      const systemStatusData: SystemStatus = {
        providersAccess,
        totalJobs: systemStatus.dataJobs.length + systemStatus.tokenJobs.length,
        completedJobs: systemStatus.dataJobs.filter(job => job.status === 'completed').length +
          systemStatus.tokenJobs.filter(job => job.status === 'completed').length,
        failedJobs: systemStatus.dataJobs.filter(job => job.status === 'failed').length +
          systemStatus.tokenJobs.filter(job => job.status === 'failed').length,
        lastUpdate: new Date()
      };

      setStatus(systemStatusData);
    } catch (error) {
      console.error('Erro geral ao buscar status:', error);
      // Cria status vazio em caso de erro
      setStatus({
        providersAccess: [],
        totalJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        lastUpdate: new Date()
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Executar captura de token manualmente
  const executeTokenCapture = async (providerAccessId: string) => {
    if (executingToken === providerAccessId) return;

    setExecutingToken(providerAccessId);
    setTokenError(null); // Limpa erro anterior

    try {
      const jobManager = new ScrapingJobManager();
      await jobManager.executeTokenCaptureManually(providerAccessId);

      // Aguarda um pouco e atualiza o status
      setTimeout(() => {
        fetchStatus();
      }, 2000);
    } catch (error) {
      console.error(`Erro ao executar captura de token para ${providerAccessId}:`, error);
      setTokenError(`Erro na captura: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setExecutingToken(null);
    }
  };

  // Executar coleta de dados manualmente
  const executeDataCollection = async (providerId: string) => {
    if (executingData === providerId) return;

    setExecutingData(providerId);
    setDataError(null); // Limpa erro anterior
    setCancelDataCollection(false); // Reset do cancelamento

    try {
      const jobManager = new ScrapingJobManager();

      // Buscar usinas deste provider
      const usinasSnapshot = await getDocs(collection(db, 'usinas'));
      const usinas = usinasSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Usina));

      const usinasDoProvider = usinas.filter(u => u.providerId === providerId);
      console.log(`🚀 Iniciando coleta para ${usinasDoProvider.length} usinas do provider ${providerId}`);
      
      setCollectionProgress({ current: 0, total: usinasDoProvider.length });

      // Executar coleta para cada usina
      for (let i = 0; i < usinasDoProvider.length; i++) {
        // Verificar se foi cancelado
        if (cancelDataCollection) {
          console.log(`⏹️ Coleta cancelada pelo usuário na usina ${i + 1}/${usinasDoProvider.length}`);
          setDataError(`Coleta cancelada pelo usuário (${i}/${usinasDoProvider.length} usinas processadas)`);
          break;
        }

        setCollectionProgress({ current: i + 1, total: usinasDoProvider.length });
        
        const usina = usinasDoProvider[i];
        try {
          console.log(`📊 Processando usina ${i + 1}/${usinasDoProvider.length}: ${usina.name || usina.id}`);
          await jobManager.executeDataCollectionManually(usina.id);
        } catch (error) {
          console.error(`❌ Erro ao coletar dados da usina ${usina.id}:`, error);
          // Continua para próxima usina em caso de erro
        }
      }

      if (!cancelDataCollection) {
        console.log(`✅ Coleta concluída para todas as ${usinasDoProvider.length} usinas`);
        // Aguarda um pouco e atualiza o status
        setTimeout(() => {
          fetchStatus();
        }, 3000);
      }
    } catch (error) {
      console.error(`Erro ao executar coleta de dados para ${providerId}:`, error);
      setDataError(`Erro na coleta: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setExecutingData(null);
      setCancelDataCollection(false);
      setCollectionProgress(null);
    }
  };

  // Cancelar coleta de dados
  const cancelDataCollectionProcess = () => {
    setCancelDataCollection(true);
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  const getTokenStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'text-green-600 bg-green-50';
      case 'expired':
        return 'text-yellow-600 bg-yellow-50';
      case 'missing':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getTokenStatusText = (status: string) => {
    switch (status) {
      case 'valid':
        return 'Válido';
      case 'expired':
        return 'Expirado';
      case 'missing':
        return 'Ausente';
      default:
        return 'Desconhecido';
    }
  };

  const getProviderStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50';
      case 'running':
        return 'text-blue-600 bg-blue-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'idle':
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getProviderStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return 'Funcionando';
      case 'running':
        return 'Executando';
      case 'error':
        return 'Erro';
      case 'idle':
      default:
        return 'Inativo';
    }
  };

  // Se não houver dados ou providers, mostrar mensagem informativa
  if (!status || status.providersAccess.length === 0) {
    return (
      <WidgetCard title="Status dos Providers" className="m-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <Database className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">
              {!status ? 'Carregando status...' : 'Nenhum provider configurado'}
            </p>
            <p className="text-xs text-gray-400 mt-1">
              {!status ? 'Aguarde...' : 'Configure providers no sistema para ver o status'}
            </p>
            {!status && (
              <div className="mt-2">
                <RefreshCw className="h-4 w-4 text-gray-400 mx-auto animate-spin" />
              </div>
            )}
          </div>
        </div>
      </WidgetCard>
    );
  }

  if (!status) {
    return (
      <WidgetCard title="Status dos Providers" className="m-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin" />
            <p className="text-gray-500">Carregando status...</p>
          </div>
        </div>
      </WidgetCard>
    );
  }

  return (
    <WidgetCard title="Status dos Providers" className="m-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Zap className="h-4 w-4" />
            <span>Monitoramento do sistema de scraping</span>
          </div>
          <Button
            onClick={fetchStatus}
            disabled={refreshing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? "Atualizando..." : "Atualizar"}
          </Button>
        </div>

        {/* Status Geral do Sistema */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Database className="h-5 w-5 text-blue-600" />
              <h3 className="font-medium text-gray-900">Total de Jobs</h3>
            </div>
            <div className="text-2xl font-bold text-gray-900">{status.totalJobs}</div>
            <p className="text-xs text-gray-500 mt-1">Jobs executados</p>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <h3 className="font-medium text-gray-900">Jobs Concluídos</h3>
            </div>
            <div className="text-2xl font-bold text-green-600">{status.completedJobs}</div>
            <p className="text-xs text-gray-500 mt-1">Executados com sucesso</p>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <h3 className="font-medium text-gray-900">Jobs Falharam</h3>
            </div>
            <div className="text-2xl font-bold text-red-600">{status.failedJobs}</div>
            <p className="text-xs text-gray-500 mt-1">Execuções com erro</p>
          </div>
        </div>

        {/* Status dos Acessos aos Providers */}
        <div className="border rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">📊 Status dos Providers e seus Acessos</h3>
          <div className="space-y-3">
            {status.providersAccess.map((provider) => (
              <div key={`${provider.providerId}-${provider.accessName}`} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div>
                      <h4 className="font-medium text-gray-900">{provider.providerName}</h4>
                      <p className="text-xs text-gray-500">Acesso: {provider.accessName}</p>
                    </div>
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getProviderStatusColor(provider.status)}`}>
                      {getProviderStatusText(provider.status)}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div>
                    <span className="text-gray-500">Usinas:</span>
                    <div className="font-medium">{provider.totalUsinas} total ({provider.usinasComDados} com dados)</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Último Token:</span>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {provider.lastTokenUpdate ?
                          provider.lastTokenUpdate.toLocaleString('pt-BR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          }) :
                          'Nunca'
                        }
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTokenStatusColor(provider.tokenStatus)}`}>
                        {getTokenStatusText(provider.tokenStatus)}
                      </span>
                      {/* Botão de captura apenas se token ausente ou vencido */}
                      {(provider.tokenStatus === 'missing' || provider.tokenStatus === 'expired') && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => executeTokenCapture(provider.providerAccessId)}
                          disabled={executingToken === provider.providerAccessId}
                          className="h-6 px-2"
                        >
                          {executingToken === provider.providerAccessId ? (
                            <>
                              <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                              Capturando...
                            </>
                          ) : (
                            <>
                              <Key className="h-3 w-3" />
                              Capturar
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                    {/* Aviso de erro do token */}
                    {tokenError && (
                      <div className="text-xs text-red-600 mt-1">
                        ⚠️ {tokenError}
                      </div>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-500">Última Coleta:</span>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {provider.lastDataCollection ?
                          provider.lastDataCollection.toLocaleString('pt-BR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          }) :
                          'Nunca'
                        }
                      </span>
                      {executingData === provider.providerId ? (
                        <div className="flex items-center gap-2">
                          {collectionProgress && (
                            <span className="text-xs text-gray-600">
                              {collectionProgress.current}/{collectionProgress.total}
                            </span>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelDataCollectionProcess}
                            className="h-6 px-2 text-red-600 hover:text-red-700"
                            title="Cancelar coleta"
                          >
                            ⏹️ Cancelar
                          </Button>
                        </div>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => executeDataCollection(provider.providerId)}
                          disabled={provider.tokenStatus !== 'valid'}
                          className="h-6 px-2"
                          title={provider.tokenStatus !== 'valid' ? 'Token necessário para coleta' : 'Coletar dados'}
                        >
                          <Download className="h-3 w-3" />
                          Coletar
                        </Button>
                      )}
                    </div>
                    {/* Aviso de erro da coleta */}
                    {dataError && (
                      <div className="text-xs text-red-600 mt-1">
                        ⚠️ {dataError}
                      </div>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-500">Progresso:</span>
                    <div className="font-medium">
                      {provider.totalUsinas > 0 ?
                        `${Math.round((provider.usinasComDados / provider.totalUsinas) * 100)}%` :
                        '0%'
                      }
                    </div>
                  </div>
                </div>

                {/* Barra de Progresso */}
                {provider.totalUsinas > 0 && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(provider.usinasComDados / provider.totalUsinas) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Informações Adicionais */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• <strong>Token:</strong> Necessário para autenticação com os painéis dos providers</p>
          <p>• <strong>Dados:</strong> Coletados automaticamente conforme agendamento</p>
          <p>• <strong>Status:</strong> Monitoramento em tempo real dos providers</p>
          <p>• <strong>Última atualização:</strong> {status.lastUpdate.toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}</p>
        </div>
      </div>
    </WidgetCard>
  );
}

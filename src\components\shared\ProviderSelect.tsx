import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { db } from "@/lib/firebase";
import { Provider } from "@/types/provider.types";
import { collection, getDocs } from "firebase/firestore";
import { useEffect, useState } from "react";

// Buscar providers do Firebase
const getProviders = async () => {
  const providers = await getDocs(collection(db, 'providers'));
  return providers.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Provider[];
}

interface ProviderSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export default function ProviderSelect({
  value,
  onChange,
  placeholder = "Selecione um provedor",
  className,
  disabled = false,
}: ProviderSelectProps) {
  const [providers, setProviders] = useState<Provider[]>([]);

  useEffect(() => {
    const fetchProviders = async () => {
      const providers = await getProviders();
      setProviders(providers);
    };
    fetchProviders();
  }, []);

  console.log(providers);

  return (
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {providers.map((provider) => (
          <SelectItem key={provider.id} value={provider.id}>
            {provider.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

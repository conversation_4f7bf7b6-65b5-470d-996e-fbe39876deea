import CardLocationData from "@/components/shared/card-location-data";
import type {
  CampusData,
  DataByDay,
  DataCumulative,
  DataEnergyNetwork,
  DataLive,
} from "@/types";
import CardCurrentStats from "@shared/card-current-data";
import CardData from "@shared/card-data";
import CardStatus from "@shared/card-status";
import { DateNavigator } from "@shared/date-navigator";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import BarChart from "../dashboard/components/bar-chart";
import StackedAreaChart from "../dashboard/components/stacked-area-chart";

// Dynamic mock data import utility
const importMockData = (campusId: string) => {
  try {
    return {
      campusDataMock: import(`@mocks/${campusId}/data_campus.json`).then(
        (m) => m.default
      ),
      dataByDayMock: import(`@mocks/${campusId}/data_by_day.json`).then(
        (m) => m.default
      ),
      dataCumulativeMock: import(
        `@mocks/${campusId}/data_cumulative.json`
      ).then((m) => m.default),
      dataLiveMock: import(`@mocks/${campusId}/data_live.json`).then(
        (m) => m.default
      ),
      dataEnergyNetworkMock: import(
        `@mocks/${campusId}/data_energy_network.json`
      ).then((m) => m.default),
    };
  } catch (error) {
    console.error(`Failed to import mock data for campus: ${campusId}`, error);
    return null;
  }
};

export default function CampusDashboardPage() {
  const { id } = useParams();

  const [mockArrayDay, setMockArrayDay] = useState(2);

  const [campusData, setCampusData] = useState<CampusData>();
  const [dataByDay, setDataByDay] = useState<DataByDay[]>();
  const [dataCumulative, setDataCumulative] = useState<DataCumulative>();
  const [dataLive, setDataLive] = useState<DataLive>();
  const [dataEnergyNetwork, setDataEnergyNetwork] =
    useState<DataEnergyNetwork[]>();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    if (id) {
      const loadMockData = async () => {
        try {
          const mockData = await importMockData(id);

          if (mockData) {
            setCampusData(await mockData.campusDataMock);
            setDataByDay(await mockData.dataByDayMock);
            setDataCumulative(await mockData.dataCumulativeMock);
            setDataLive(await mockData.dataLiveMock);
            setDataEnergyNetwork(await mockData.dataEnergyNetworkMock);
            setError(null);
          } else {
            setError(`No mock data found for campus: ${id}`);
          }
        } catch (err) {
          console.error("Erro ao definir dados mock:", err);
          setError(`Failed to load mock data for campus: ${id}`);
        } finally {
          setLoading(false);
        }
      };

      loadMockData();
    } else {
      // Caso não haja ID, defina um estado apropriado
      setCampusData(undefined);
      setDataByDay(undefined);
      setDataCumulative(undefined);
      setDataLive(undefined);
      setDataEnergyNetwork(undefined);
      setError("ID do campus não fornecido."); // Ou trate como um estado não errôneo
      setLoading(false);
    }
  }, [id]);

  if (loading) return <div>Carregando dados do campus...</div>;
  if (error) return <div>{error}</div>;
  if (
    !campusData ||
    !dataByDay ||
    !dataCumulative ||
    !dataLive ||
    !dataEnergyNetwork
  )
    return <div>Dados do campus não disponíveis ou ID inválido.</div>;

  // peakPowerToday: maior valor de graph.30min.generation do primeiro array
  const peakPowerToday = dataByDay?.[mockArrayDay].graph["30min"].reduce(
    (max: number, entry: { generation: number | null }) =>
      Math.max(max, entry.generation || 0),
    0
  );

  // specificYield: energia gerada total / capacidade instalada
  const specificYield =
    (dataEnergyNetwork?.[0].energy_generated || 0) /
    (campusData?.capacity * 30);

  // capacityFactor: energia gerada total / (capacidade instalada * horas no período)
  const capacityFactor =
    ((dataEnergyNetwork?.[0].energy_generated || 0) /
      (campusData?.capacity * (24 * 30))) *
    100;

  function changeMockArrayDay(date: number) {
    // Converte date para string 2025-05-10
    // const dateStr = date.toISOString().split("T")[0];
    // const mockArrayDay = dataByDay?.findIndex((item) => item.date === dateStr);
    // if (mockArrayDay !== undefined) {
    //   setMockArrayDay(mockArrayDay);
    // }
    // Utilizando mocks, incremente de 0 a 2
    // Se menor que 0, igual a 0, se menor que 2, igual a 2
    if (mockArrayDay + date < 0) {
      setMockArrayDay(0);
    } else if (mockArrayDay + date > 2) {
      setMockArrayDay(2);
    } else {
      setMockArrayDay(mockArrayDay + date);
    }
  }

  /**
   * Quando necessário cálculo de valor financeiro gerado,
   * sempre fazer cálculo com valor hora de dataEnergyNetwork.[x].kwh_value
   */

  return (
    <>
      <div className="flex gap-4 mb-5 flex-row justify-between">
        <DateNavigator onDateChange={(date) => changeMockArrayDay(date)} />
        <h1 className="text-slate-700 text-right content-center">
          {campusData?.name}
        </h1>
      </div>
      <div
        className="
          grid
          gap-6 xl:gap-8
          grid-cols-1
          md:grid-cols-3
          xl:grid-cols-6
          3xl:grid-cols-10
          xl:mt-5
          @container
        "
      >
        <div
          className="
            grid
            gap-6 xl:gap-8
            grid-cols-0
            md:grid-cols-2
            md:col-span-2
            xl:col-span-4
            xl:grid-cols-3
            3xl:col-span-6
            grid-flow-dense
            @container
          "
        >
          <div
            className="
              grid
              grid-cols-1
              grid-flow-dense
              col-span-2
              md:col-span-1
              @container
            "
          >
            <CardCurrentStats
              title="Geração Atual"
              currentValue={dataLive?.current_power || 0}
              maxValue={peakPowerToday || 0}
            />
            <CardStatus
              status={dataLive?.status || 0}
              inverter={dataLive?.inverter || []}
              color="orange"
            />
          </div>
          <div
            className="
              grid
              gap-4 xl:gap-6
              grid-cols-1
              md:col-span-2
              xl:grid-cols-2
              grid-flow-dense
              auto-rows-[minmax(78px,auto)]
              @container
            "
          >
            <CardData
              title="Geração Hoje"
              value={dataByDay?.[mockArrayDay].energy_generated_today || 0}
              unity="kWh"
              color="yellow"
              img="energia-solar.png"
            />
            <CardData
              title="Geração 7d"
              value={
                dataByDay?.[mockArrayDay].energy_generated_last_7_days || 0
              }
              unity="kWh"
              helper="Acumulado da geração dos últimos 7 dias"
            />
            <CardData
              title="Geração 30d"
              value={dataEnergyNetwork?.[0].energy_generated || 0}
              unity="kWh"
              helper="Acumulado da geração dos últimos 30 dias"
            />
            <CardData
              title="Valor Financeiro 30d"
              value={dataEnergyNetwork?.[0].financial_value_generated || 0}
              unity="R$"
              helper="Valor financeiro gerado nos últimos 30 dias"
            />
            <CardData
              title="Rendimento Específico 30d"
              value={specificYield || 0}
              unity="kWh/kWp"
              helper="Energia Gerada Total (kWh) / Capacidade Instalada (kWp)"
            />
            <CardData
              title="Fator de Capacidade 30d"
              value={capacityFactor || 0}
              unity="%"
              helper="Energia Gerada Total (kWh) / (Capacidade Instalada (kWp) * Número de Horas no Período)"
            />
          </div>
          <div
            className="
            grid
            gap-6 xl:gap-8
            mt-2
            grid-cols-1
            col-span-3
            @container"
          >
            <StackedAreaChart
              title="Geração x Irradiação Hoje"
              className="col-span-full xl:col-span-6"
              data={dataByDay[mockArrayDay].graph["30min"]}
            />
            {dataCumulative?.graph && (
              <BarChart
                graphData={dataCumulative.graph}
                className="col-span-full xl:col-span-6"
              />
            )}
          </div>
        </div>

        <div
          className="
            grid
            gap-4 xl:gap-6
            grid-cols-1
            md:col-span-1
            xl:col-span-2
            3xl:col-span-4
          "
        >
          <CardLocationData
            campusData={campusData}
            dataEnergyNetwork={dataEnergyNetwork}
          />
        </div>
      </div>
    </>
  );
}
